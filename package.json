{"name": "sinoair-agent-portal-vue", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "vite --mode development", "prod": "vite --mode production", "test": "vite --mode test", "staging": "vite --mode staging", "build": "vite build --mode production", "build:dev": "vite build --mode development", "build:test": "vite build --mode test", "build:staging": "vite build --mode staging", "build:prod": "vite build --mode production", "preview": "vite preview", "preview:test": "vite preview --mode test", "preview:staging": "vite preview --mode staging", "preview:prod": "vite preview --mode production", "lint": "eslint . --ext vue,js,jsx,cjs,mjs --fix --ignore-path .gitignore"}, "dependencies": {"@fortawesome/fontawesome-free": "^6.4.0", "@vueuse/core": "^10.5.0", "axios": "^1.5.0", "bootstrap": "^5.3.0", "dayjs": "^1.11.0", "marked": "^16.1.1", "pinia": "^2.1.0", "sweetalert2": "^11.7.0", "vue": "^3.3.0", "vue-router": "^4.2.0", "vue-toastification": "^2.0.0-rc.5"}, "devDependencies": {"@vitejs/plugin-vue": "^4.4.0", "@vue/eslint-config-prettier": "^8.0.0", "eslint": "^8.50.0", "eslint-plugin-vue": "^9.17.0", "prettier": "^3.0.0", "sass": "^1.69.0", "vite": "^4.5.0"}}