<template>
  <footer class="footer bg-light border-top">
    <div class="container">
      <!-- 版权信息 -->
      <div class="row py-3">
        <div class="col-md-6">
          <p class="footer-copyright text-muted mb-0">
            © {{ currentYear }} 智能体矩阵平台. 保留所有权利.
          </p>
        </div>
        <div class="col-md-6 text-md-end">
          <div class="footer-legal">
            <router-link to="/legal/privacy" class="footer-link me-3">隐私政策</router-link>
            <router-link to="/legal/terms" class="footer-link me-3">服务条款</router-link>
            <router-link to="/legal/agreement" class="footer-link">使用协议</router-link>
          </div>
        </div>
      </div>
    </div>
  </footer>
</template>

<script setup>
import { computed } from 'vue'

const currentYear = computed(() => new Date().getFullYear())
</script>

<style scoped>
.footer {
  margin-top: auto;
  background: #f8f9fa;
}

.footer-copyright {
  font-size: 0.875rem;
}

.footer-legal {
  font-size: 0.875rem;
}

.footer-link {
  color: #6c757d;
  text-decoration: none;
  transition: color 0.3s ease;
}

.footer-link:hover {
  color: #009EF7;
  text-decoration: none;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .col-md-6.text-md-end {
    text-align: center !important;
    margin-top: 1rem;
  }

  .footer-legal {
    display: flex;
    justify-content: center;
    gap: 1rem;
  }
}

/* 深色主题适配 */
.theme-dark .footer {
  background: #1e1e2d;
  border-color: #2b2b40;
}

.theme-dark .footer-link {
  color: #a1a5b7;
}

.theme-dark .footer-link:hover {
  color: #009EF7;
}

.theme-dark .footer-copyright {
  color: #a1a5b7 !important;
}

.theme-dark .border-top {
  border-color: #3f3f56 !important;
}
</style>
