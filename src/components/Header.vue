<template>
  <header class="header-container">
    <div class="header-content">
      <!-- Logo -->
      <div class="header-logo">
        <router-link to="/" class="navbar-brand d-flex align-items-center">
          <i class="fas fa-brain me-2"></i>
          <span class="fw-bold">智能体矩阵</span>
        </router-link>
      </div>

      <!-- 移动端菜单按钮 - 仅在登录后显示 -->
      <button
        class="navbar-toggler d-lg-none"
        type="button"
        @click="toggleMobileMenu"
        :class="{ collapsed: !showMobileMenu }"
        v-if="authStore.isAuthenticated"
      >
        <span class="navbar-toggler-icon"></span>
      </button>

      <!-- 中间菜单 - 仅在登录后显示 -->
      <div class="header-nav" :class="{ show: showMobileMenu }" v-if="authStore.isAuthenticated">
        <ul class="navbar-nav">
          <li class="nav-item">
            <router-link to="/" class="nav-link" :class="{ active: $route.name === 'Home' }">
              <i class="fas fa-home me-2"></i>首页
            </router-link>
          </li>

          <li class="nav-item">
            <router-link to="/api-docs" class="nav-link" :class="{ active: $route.name === 'ApiDocs' }">
              <i class="fas fa-book me-2"></i>API文档
            </router-link>
          </li>
          <li class="nav-item">
            <router-link to="/agent/square" class="nav-link" :class="{ active: isAgentPage }">
              <i class="fas fa-th-large me-2"></i>AGENT广场
            </router-link>
          </li>
        </ul>
      </div>

      <!-- 右侧用户菜单 -->
      <div class="header-user" :class="{ show: showMobileMenu }">
        <ul class="navbar-nav">
          <li class="nav-item dropdown" v-if="authStore.isAuthenticated">
            <a
              class="nav-link dropdown-toggle d-flex align-items-center"
              href="#"
              @click.prevent="toggleUserMenu"
              :class="{ show: showUserMenu }"
            >
              <div class="user-avatar me-2">
                {{ getUserInitial }}
              </div>
              <span>{{ authStore.user?.realName || '用户' }}</span>
            </a>
            <ul class="dropdown-menu dropdown-menu-end" :class="{ show: showUserMenu }">
              <li>
                <router-link to="/dashboard" class="dropdown-item">
                  <i class="fas fa-tachometer-alt me-2"></i>仪表盘
                </router-link>
              </li>
              <li>
                <router-link to="/dashboard/subscriptions" class="dropdown-item">
                  <i class="fas fa-credit-card me-2"></i>我的订阅
                </router-link>
              </li>
              <li>
                <router-link to="/dashboard/history" class="dropdown-item">
                  <i class="fas fa-history me-2"></i>调用历史
                </router-link>
              </li>
              <li>
                <router-link to="/dashboard/api-keys" class="dropdown-item">
                  <i class="fas fa-key me-2"></i>API密钥
                </router-link>
              </li>
              <li><hr class="dropdown-divider"></li>
              <li>
                <a class="dropdown-item" href="#" @click.prevent="handleLogout">
                  <i class="fas fa-sign-out-alt me-2"></i>退出登录
                </a>
              </li>
            </ul>
          </li>
          <li class="nav-item" v-else>
            <router-link to="/auth/login" class="btn btn-outline-light">
              <i class="fas fa-sign-in-alt me-2"></i>登录
            </router-link>
          </li>
        </ul>
      </div>
    </div>
  </header>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { useToast } from 'vue-toastification'

const router = useRouter()
const route = useRoute()
const authStore = useAuthStore()
const toast = useToast()

// 响应式数据
const showMobileMenu = ref(false)
const showUserMenu = ref(false)

// 计算属性
const getUserInitial = computed(() => {
  const name = authStore.user?.realName || authStore.user?.email || 'U'
  return name.charAt(0).toUpperCase()
})

const isAgentPage = computed(() => {
  return ['AgentSquare', 'AgentDetail'].includes(route.name)
})

// 方法
const toggleMobileMenu = () => {
  showMobileMenu.value = !showMobileMenu.value
}

const toggleUserMenu = () => {
  showUserMenu.value = !showUserMenu.value
}

const handleLogout = () => {
  authStore.logout()
  toast.success('已成功退出登录')
  router.push('/')
}

// 点击外部关闭菜单
const handleClickOutside = (event) => {
  if (!event.target.closest('.dropdown')) {
    showUserMenu.value = false
  }
  if (!event.target.closest('.navbar-collapse') && !event.target.closest('.navbar-toggler')) {
    showMobileMenu.value = false
  }
}

onMounted(() => {
  document.addEventListener('click', handleClickOutside)
})

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
})
</script>

<style scoped>
/* Header样式 - 重新设计 */
.header-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1030;
  height: var(--kt-header-height);
  background-color: var(--kt-header-bg);
  border-bottom: 1px solid var(--kt-header-border-color);
  box-shadow: 0 0.5rem 1.5rem 0.5rem rgba(0, 0, 0, 0.075);
  transition: all 0.2s ease;
}

/* 确保桌面端布局 */
@media (min-width: 992px) {
  .header-nav,
  .header-user {
    display: flex !important;
  }

  .navbar-toggler {
    display: none !important;
  }

  /* 确保中间菜单在桌面端正确显示 */
  .header-nav .navbar-nav {
    display: flex !important;
    flex-direction: row !important;
  }

  .header-nav .nav-item {
    display: flex !important;
  }

  .header-nav .nav-link {
    display: flex !important;
    align-items: center !important;
  }

  /* 桌面端下拉菜单定位 */
  .dropdown-menu {
    position: absolute !important;
    top: 100% !important;
    right: 0 !important;
    left: auto !important;
    transform: none !important;
  }
}

/* Header内容容器 */
.header-content {
  display: flex;
  align-items: center;
  height: 100%;
  width: 100%;
  padding: 0 1rem;
  position: relative;
}

/* Logo区域 - 固定在左侧 */
.header-logo {
  position: absolute;
  left: 1rem;
  top: 50%;
  transform: translateY(-50%);
  z-index: 1031;
}

/* 中间导航区域 - 绝对居中 */
.header-nav {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  z-index: 1030;
  display: flex;
  align-items: center;
  justify-content: center;
}

.header-nav .navbar-nav {
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0;
  padding: 0;
  list-style: none;
  white-space: nowrap;
  gap: 0.5rem;
}

/* 右侧用户区域 - 固定在右侧 */
.header-user {
  position: absolute;
  right: 1rem;
  top: 50%;
  transform: translateY(-50%);
  z-index: 1031;
}

.header-user .navbar-nav {
  display: flex;
  align-items: center;
  margin: 0;
  padding: 0;
  list-style: none;
}

/* 下拉菜单容器需要相对定位 */
.nav-item.dropdown {
  position: relative;
}

.navbar-brand {
  font-size: 1.5rem;
  font-weight: 700;
  text-decoration: none;
  color: #ffffff !important;
  transition: all 0.2s ease;
}

.navbar-brand:hover {
  color: var(--kt-primary) !important;
  text-decoration: none;
}

.nav-link {
  color: #cbd5e1 !important;
  font-weight: 600;
  font-size: 0.9rem;
  transition: all 0.3s ease;
  border-radius: var(--kt-border-radius-sm);
  margin: 0 0.25rem;
  padding: 0.75rem 1.25rem !important;
  position: relative;
  white-space: nowrap;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
}

.nav-link:hover {
  color: #ffffff !important;
  background-color: rgba(255, 255, 255, 0.12);
  text-decoration: none;
  transform: translateY(-1px);
}

.nav-link.active {
  color: var(--kt-primary) !important;
  background-color: rgba(0, 158, 247, 0.15);
}

.nav-link.active::after {
  content: '';
  position: absolute;
  bottom: -1px;
  left: 1.25rem;
  right: 1.25rem;
  height: 2px;
  background-color: var(--kt-primary);
  border-radius: 1px;
}

/* 用户头像样式 - 与HTML版本一致 */
.user-avatar {
  width: 35px;
  height: 35px;
  border-radius: 50%;
  background: linear-gradient(135deg, #009ef7 0%, #4facfe 100%);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.875rem;
  font-weight: 600;
  text-transform: uppercase;
  transition: all 0.3s ease;
  border: 2px solid rgba(255, 255, 255, 0.2);
}

.user-avatar:hover {
  transform: scale(1.05);
  box-shadow: 0 2px 8px rgba(0, 158, 247, 0.3);
}

/* 下拉菜单样式 */
.dropdown-menu {
  position: absolute;
  top: 100%;
  right: 0;
  left: auto;
  z-index: 1050;
  min-width: 250px;
  padding: 0.5rem 0;
  margin: 0.5rem 0 0;
  background-color: #ffffff;
  border: 1px solid #eff2f5;
  border-radius: var(--kt-border-radius);
  box-shadow: var(--kt-box-shadow);
  display: none;
}

.dropdown-menu.show {
  display: block;
}

.dropdown-item {
  display: flex;
  align-items: center;
  width: 100%;
  padding: 0.75rem 1.5rem;
  clear: both;
  font-weight: 400;
  color: var(--kt-text-gray-700);
  text-align: inherit;
  text-decoration: none;
  white-space: nowrap;
  background-color: transparent;
  border: 0;
  transition: all 0.2s ease;
}

.dropdown-item:hover {
  color: var(--kt-text-dark);
  background-color: var(--kt-light);
}

.dropdown-item i {
  margin-right: 0.75rem;
  font-size: 1rem;
  color: var(--kt-text-muted);
}

/* 登录按钮样式 - 与HTML版本一致 */
.btn-outline-light {
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.6rem 1.5rem;
  font-size: 0.875rem;
  font-weight: 600;
  text-decoration: none;
  border-radius: 25px;
  border: 2px solid transparent;
  background: linear-gradient(135deg, #009ef7 0%, #4facfe 100%);
  color: #ffffff;
  box-shadow: 0 4px 15px rgba(0, 158, 247, 0.3);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
  white-space: nowrap;
  text-transform: none;
  letter-spacing: 0.5px;
}

.btn-outline-light::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.6s;
}

.btn-outline-light:hover {
  color: #ffffff;
  text-decoration: none;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 158, 247, 0.4);
  background: linear-gradient(135deg, #4facfe 0%, #009ef7 100%);
}

.btn-outline-light:hover::before {
  left: 100%;
}

.btn-outline-light:active {
  transform: translateY(-1px);
  box-shadow: 0 6px 20px rgba(0, 158, 247, 0.35);
}

.btn-outline-light i {
  font-size: 0.8rem;
  margin-right: 0.5rem;
  transition: transform 0.3s ease;
}

.btn-outline-light:hover i {
  transform: translateX(2px);
}

/* 移动端汉堡菜单按钮 */
.navbar-toggler {
  border: none;
  background: none;
  padding: 0.25rem 0.5rem;
  font-size: 1.25rem;
  line-height: 1;
  color: rgba(255, 255, 255, 0.8);
  border-radius: 0.25rem;
  transition: all 0.15s ease-in-out;
}

.navbar-toggler:hover {
  color: rgba(255, 255, 255, 1);
}

.navbar-toggler-icon {
  display: inline-block;
  width: 1.5em;
  height: 1.5em;
  vertical-align: middle;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='rgba%28255, 255, 255, 0.8%29' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: center;
  background-size: 100%;
}

/* 移动端适配 */
@media (max-width: 991.98px) {
  .header-content {
    position: relative;
    padding: 0 1rem;
  }

  .header-logo {
    position: static;
    transform: none;
  }

  .navbar-brand {
    font-size: 1.25rem;
  }

  /* 移动端汉堡菜单按钮定位 */
  .navbar-toggler {
    position: absolute;
    right: 1rem;
    top: 50%;
    transform: translateY(-50%);
  }

  .header-nav,
  .header-user {
    display: none;
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: rgba(0, 0, 0, 0.95);
    border-radius: 0 0 var(--kt-border-radius) var(--kt-border-radius);
    padding: 1rem;
    flex-direction: column;
    width: 100%;
    z-index: 1029;
    transform: none;
  }

  .header-nav.show,
  .header-user.show {
    display: flex;
  }

  .header-nav .navbar-nav,
  .header-user .navbar-nav {
    flex-direction: column;
    width: 100%;
  }

  .nav-link {
    margin: 0.25rem 0;
    text-align: center;
    padding: 0.75rem 1rem;
  }

  .dropdown-menu {
    position: static !important;
    transform: none !important;
    box-shadow: none;
    background: rgba(255, 255, 255, 0.1);
    border-radius: var(--kt-border-radius-sm);
    margin-top: 0.5rem;
  }

  .dropdown-item {
    color: rgba(255, 255, 255, 0.8);
  }

  .dropdown-item:hover {
    background-color: rgba(255, 255, 255, 0.1);
    color: white;
  }

  .btn-outline-light {
    display: block;
    width: fit-content;
    margin: 0 auto;
  }
}

@media (max-width: 575.98px) {
  .header-container {
    padding: 0 0.75rem;
  }

  .navbar-brand {
    font-size: 1.1rem;
  }

  .navbar-brand i {
    font-size: 1rem;
  }

  .user-avatar {
    width: 32px;
    height: 32px;
    font-size: 0.75rem;
  }

  .dropdown-menu {
    min-width: 200px;
    right: 0;
    left: auto;
  }

  .btn-outline-light {
    padding: 0.45rem 1rem;
    font-size: 0.75rem;
    border-radius: 18px;
    box-shadow: 0 3px 12px rgba(0, 158, 247, 0.25);
  }

  .btn-outline-light:hover {
    box-shadow: 0 6px 20px rgba(0, 158, 247, 0.35);
  }
}
</style>
