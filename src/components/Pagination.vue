<template>
  <nav v-if="totalPages > 1" class="pagination-container">
    <ul class="pagination justify-content-center">
      <!-- 上一页 -->
      <li class="page-item" :class="{ disabled: currentPage === 1 }">
        <a 
          class="page-link" 
          href="#" 
          @click.prevent="changePage(currentPage - 1)"
          :tabindex="currentPage === 1 ? -1 : 0"
        >
          <i class="fas fa-chevron-left"></i>
          <span class="d-none d-sm-inline ms-1">上一页</span>
        </a>
      </li>

      <!-- 第一页 -->
      <li v-if="showFirstPage" class="page-item" :class="{ active: currentPage === 1 }">
        <a class="page-link" href="#" @click.prevent="changePage(1)">1</a>
      </li>

      <!-- 左侧省略号 -->
      <li v-if="showLeftEllipsis" class="page-item disabled">
        <span class="page-link">...</span>
      </li>

      <!-- 中间页码 -->
      <li 
        v-for="page in middlePages" 
        :key="page" 
        class="page-item" 
        :class="{ active: currentPage === page }"
      >
        <a class="page-link" href="#" @click.prevent="changePage(page)">{{ page }}</a>
      </li>

      <!-- 右侧省略号 -->
      <li v-if="showRightEllipsis" class="page-item disabled">
        <span class="page-link">...</span>
      </li>

      <!-- 最后一页 -->
      <li v-if="showLastPage" class="page-item" :class="{ active: currentPage === totalPages }">
        <a class="page-link" href="#" @click.prevent="changePage(totalPages)">{{ totalPages }}</a>
      </li>

      <!-- 下一页 -->
      <li class="page-item" :class="{ disabled: currentPage === totalPages }">
        <a 
          class="page-link" 
          href="#" 
          @click.prevent="changePage(currentPage + 1)"
          :tabindex="currentPage === totalPages ? -1 : 0"
        >
          <span class="d-none d-sm-inline me-1">下一页</span>
          <i class="fas fa-chevron-right"></i>
        </a>
      </li>
    </ul>

    <!-- 页码信息 -->
    <div class="pagination-info text-center mt-2">
      <small class="text-muted">
        第 {{ currentPage }} 页，共 {{ totalPages }} 页，总计 {{ total }} 条记录
      </small>
    </div>
  </nav>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
  currentPage: {
    type: Number,
    required: true
  },
  totalPages: {
    type: Number,
    required: true
  },
  total: {
    type: Number,
    required: true
  },
  maxVisiblePages: {
    type: Number,
    default: 5
  }
})

const emit = defineEmits(['page-change'])

// 计算显示的页码范围
const pageRange = computed(() => {
  const { currentPage, totalPages, maxVisiblePages } = props
  const half = Math.floor(maxVisiblePages / 2)
  
  let start = Math.max(1, currentPage - half)
  let end = Math.min(totalPages, start + maxVisiblePages - 1)
  
  if (end - start + 1 < maxVisiblePages) {
    start = Math.max(1, end - maxVisiblePages + 1)
  }
  
  return { start, end }
})

// 是否显示第一页
const showFirstPage = computed(() => {
  return pageRange.value.start > 1
})

// 是否显示最后一页
const showLastPage = computed(() => {
  return pageRange.value.end < props.totalPages
})

// 是否显示左侧省略号
const showLeftEllipsis = computed(() => {
  return pageRange.value.start > 2
})

// 是否显示右侧省略号
const showRightEllipsis = computed(() => {
  return pageRange.value.end < props.totalPages - 1
})

// 中间显示的页码
const middlePages = computed(() => {
  const pages = []
  const { start, end } = pageRange.value
  
  for (let i = start; i <= end; i++) {
    if (i !== 1 && i !== props.totalPages) {
      pages.push(i)
    }
  }
  
  return pages
})

// 切换页码
const changePage = (page) => {
  if (page >= 1 && page <= props.totalPages && page !== props.currentPage) {
    emit('page-change', page)
  }
}
</script>

<style scoped>
.pagination-container {
  margin: 2rem 0;
}

.pagination {
  margin-bottom: 0;
}

.page-link {
  color: var(--kt-primary);
  border-color: #dee2e6;
  padding: 0.5rem 0.75rem;
  transition: all 0.3s ease;
}

.page-link:hover {
  color: var(--kt-primary-dark);
  background-color: #e9ecef;
  border-color: #dee2e6;
  transform: translateY(-1px);
}

.page-item.active .page-link {
  background-color: var(--kt-primary);
  border-color: var(--kt-primary);
  color: white;
}

.page-item.disabled .page-link {
  color: #6c757d;
  background-color: #fff;
  border-color: #dee2e6;
  cursor: not-allowed;
}

.page-item.disabled .page-link:hover {
  transform: none;
}

.pagination-info {
  color: #6c757d;
  font-size: 0.875rem;
}

@media (max-width: 576px) {
  .page-link {
    padding: 0.375rem 0.5rem;
    font-size: 0.875rem;
  }
  
  .pagination-info {
    font-size: 0.75rem;
  }
}
</style>
