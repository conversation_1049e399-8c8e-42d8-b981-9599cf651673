<template>
  <div class="loading-container" :class="{ 'loading-overlay': overlay }">
    <div class="loading-content">
      <div class="loading-spinner" :class="sizeClass"></div>
      <p v-if="text" class="loading-text">{{ text }}</p>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
  size: {
    type: String,
    default: 'medium',
    validator: (value) => ['small', 'medium', 'large'].includes(value)
  },
  text: {
    type: String,
    default: ''
  },
  overlay: {
    type: Boolean,
    default: false
  }
})

const sizeClass = computed(() => {
  return `loading-spinner-${props.size}`
})
</script>

<style scoped>
.loading-container {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
}

.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.9);
  z-index: 9999;
  backdrop-filter: blur(2px);
}

.loading-content {
  text-align: center;
}

.loading-spinner {
  border-radius: 50%;
  border-style: solid;
  border-color: #e3e3e3;
  border-top-color: var(--kt-primary);
  animation: spin 1s linear infinite;
}

.loading-spinner-small {
  width: 20px;
  height: 20px;
  border-width: 2px;
}

.loading-spinner-medium {
  width: 40px;
  height: 40px;
  border-width: 4px;
}

.loading-spinner-large {
  width: 60px;
  height: 60px;
  border-width: 6px;
}

.loading-text {
  margin-top: 1rem;
  color: #666;
  font-size: 0.9rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style>
