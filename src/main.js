import { createApp } from 'vue'
import { createPinia } from 'pinia'
import router from './router'
import App from './App.vue'
import { useAuthStore } from './stores/auth'

// 环境配置
import { printEnvInfo, ENV_CONFIG } from './utils/env'

// 样式导入 - CSS已在index.html中引用
import './styles/main.scss'

// Toast 通知
import Toast from 'vue-toastification'
import 'vue-toastification/dist/index.css'

// 打印环境信息
printEnvInfo()

const app = createApp(App)
const pinia = createPinia()

// Toast 配置
const toastOptions = {
  position: 'bottom-right',
  timeout: 3000,
  closeOnClick: true,
  pauseOnFocusLoss: true,
  pauseOnHover: true,
  draggable: true,
  draggablePercent: 0.6,
  showCloseButtonOnHover: false,
  hideProgressBar: false,
  closeButton: 'button',
  icon: true,
  rtl: false
}

app.use(pinia)
app.use(router)
app.use(Toast, toastOptions)

// 应用启动后初始化认证状态
router.isReady().then(async () => {
  const authStore = useAuthStore()
  // 初始化Token请求头
  authStore.initToken()
  // 尝试从Token中恢复用户信息 - 等待完成
  try {
    await authStore.initUser()
    console.log(`[${ENV_CONFIG.APP_ENV.toUpperCase()}] 用户信息初始化完成`)
  } catch (error) {
    console.error(`[${ENV_CONFIG.APP_ENV.toUpperCase()}] 用户信息初始化失败:`, error)
  }
})

app.mount('#app')
