// 全局JavaScript库引用
// 这个文件用于在Vue项目中引用传统的JavaScript库

// 动态加载JavaScript文件
function loadScript(src) {
  return new Promise((resolve, reject) => {
    const script = document.createElement('script')
    script.src = src
    script.onload = resolve
    script.onerror = reject
    document.head.appendChild(script)
  })
}

// 动态加载CSS文件
function loadCSS(href) {
  return new Promise((resolve, reject) => {
    const link = document.createElement('link')
    link.rel = 'stylesheet'
    link.href = href
    link.onload = resolve
    link.onerror = reject
    document.head.appendChild(link)
  })
}

// 初始化全局库
export async function initGlobalLibraries() {
  try {
    // 加载jQuery
    await loadScript('/js/jquery.min.js')
    
    // 加载Bootstrap JS
    await loadScript('/js/bootstrap.bundle.min.js')
    
    // 加载SweetAlert2
    await loadScript('/js/sweetalert2.all.min.js')
    
    console.log('全局JavaScript库加载完成')
    
    // 设置全局变量
    if (window.jQuery) {
      window.$ = window.jQuery
    }
    
    return true
  } catch (error) {
    console.error('加载全局JavaScript库失败:', error)
    return false
  }
}

// Toast通知函数（使用SweetAlert2）
export function showToast(message, type = 'info') {
  if (window.Swal) {
    const config = {
      toast: true,
      position: 'top-end',
      showConfirmButton: false,
      timer: 3000,
      timerProgressBar: true,
      title: message
    }
    
    switch (type) {
      case 'success':
        window.Swal.fire({ ...config, icon: 'success' })
        break
      case 'error':
        window.Swal.fire({ ...config, icon: 'error' })
        break
      case 'warning':
        window.Swal.fire({ ...config, icon: 'warning' })
        break
      default:
        window.Swal.fire({ ...config, icon: 'info' })
    }
  } else {
    // 降级到原生alert
    alert(message)
  }
}

// 导出jQuery（如果可用）
export function getJQuery() {
  return window.jQuery || window.$
}

// 导出Bootstrap（如果可用）
export function getBootstrap() {
  return window.bootstrap
}

// 导出SweetAlert2（如果可用）
export function getSwal() {
  return window.Swal
}
