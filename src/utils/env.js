/**
 * 环境配置工具类
 */

// 获取环境变量
export const getEnv = (key, defaultValue = '') => {
  return import.meta.env[key] || defaultValue
}

// 环境配置
export const ENV_CONFIG = {
  // 应用信息
  APP_TITLE: getEnv('VITE_APP_TITLE', 'SINOAIR-AGENT 智能体门户'),
  APP_DESCRIPTION: getEnv('VITE_APP_DESCRIPTION', '基于AI的智能文档处理平台'),
  APP_ENV: getEnv('VITE_APP_ENV', 'development'),
  
  // API配置
  API_BASE_URL: getEnv('VITE_API_BASE_URL', 'http://localhost:8088'),
  API_TIMEOUT: parseInt(getEnv('VITE_API_TIMEOUT', '30000')),
  
  // 调试配置
  DEBUG: getEnv('VITE_DEBUG', 'false') === 'true',
  CONSOLE_LOG: getEnv('VITE_CONSOLE_LOG', 'false') === 'true',
  
  // 代理配置
  PROXY_ENABLED: getEnv('VITE_PROXY_ENABLED', 'true') === 'true',
  PROXY_TARGET: getEnv('VITE_PROXY_TARGET', 'http://localhost:8088')
}

// 判断环境
export const isDevelopment = () => ENV_CONFIG.APP_ENV === 'development'
export const isTest = () => ENV_CONFIG.APP_ENV === 'test'
export const isStaging = () => ENV_CONFIG.APP_ENV === 'staging'
export const isProduction = () => ENV_CONFIG.APP_ENV === 'production'

// 获取API基础URL
export const getApiBaseUrl = () => {
  // 如果是开发环境且启用了代理，使用相对路径
  if (isDevelopment() && ENV_CONFIG.PROXY_ENABLED) {
    return ''
  }
  return ENV_CONFIG.API_BASE_URL
}

// 控制台日志
export const log = (...args) => {
  if (ENV_CONFIG.DEBUG || ENV_CONFIG.CONSOLE_LOG) {
    console.log(`[${ENV_CONFIG.APP_ENV.toUpperCase()}]`, ...args)
  }
}

// 错误日志
export const error = (...args) => {
  if (ENV_CONFIG.DEBUG || ENV_CONFIG.CONSOLE_LOG) {
    console.error(`[${ENV_CONFIG.APP_ENV.toUpperCase()}]`, ...args)
  }
}

// 警告日志
export const warn = (...args) => {
  if (ENV_CONFIG.DEBUG || ENV_CONFIG.CONSOLE_LOG) {
    console.warn(`[${ENV_CONFIG.APP_ENV.toUpperCase()}]`, ...args)
  }
}

// 导出环境信息
export const getEnvInfo = () => {
  return {
    title: ENV_CONFIG.APP_TITLE,
    description: ENV_CONFIG.APP_DESCRIPTION,
    env: ENV_CONFIG.APP_ENV,
    apiBaseUrl: getApiBaseUrl(),
    debug: ENV_CONFIG.DEBUG,
    timestamp: new Date().toISOString()
  }
}

// 打印环境信息
export const printEnvInfo = () => {
  const info = getEnvInfo()
  console.group('🌍 环境配置信息')
  console.log('📱 应用标题:', info.title)
  console.log('📝 应用描述:', info.description)
  console.log('🏷️ 运行环境:', info.env)
  console.log('🔗 API地址:', info.apiBaseUrl || '使用代理')
  console.log('🐛 调试模式:', info.debug ? '开启' : '关闭')
  console.log('⏰ 启动时间:', info.timestamp)
  console.groupEnd()
}

export default ENV_CONFIG
