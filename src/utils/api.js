import axios from 'axios'
import { useAuthStore } from '@/stores/auth'
import { useToast } from 'vue-toastification'
import { ENV_CONFIG, getApiBaseUrl, log, error } from './env'

// 创建axios实例
const api = axios.create({
  baseURL: getApiBaseUrl(), // 根据环境自动选择API地址
  timeout: ENV_CONFIG.API_TIMEOUT,
  withCredentials: true, // 支持Cookie-based session
  headers: {
    'Content-Type': 'application/json',
    'X-Requested-With': 'XMLHttpRequest'
  }
})

// 打印API配置信息
log('API配置:', {
  baseURL: getApiBaseUrl(),
  timeout: ENV_CONFIG.API_TIMEOUT,
  env: ENV_CONFIG.APP_ENV
})

// 请求拦截器
api.interceptors.request.use(
  (config) => {
    // 添加JWT Token - 与AuthStore保持一致使用'token'键名
    const token = localStorage.getItem('token')
    if (token) {
      config.headers['Authorization'] = `Bearer ${token}`
    }

    // 添加CSRF token（如果需要）
    const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content')
    if (csrfToken) {
      config.headers['X-CSRF-TOKEN'] = csrfToken
    }

    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// 响应拦截器
api.interceptors.response.use(
  (response) => {
    return response
  },
  (error) => {
    const toast = useToast()
    const authStore = useAuthStore()
    
    if (error.response) {
      const { status, data } = error.response
      
      switch (status) {
        case 401:
          // 未授权，清除认证信息并跳转到登录页
          authStore.logout()
          toast.error('登录已过期，请重新登录')
          if (window.location.pathname !== '/auth/login') {
            window.location.href = '/auth/login'
          }
          break
          
        case 403:
          toast.error('没有权限访问该资源')
          break
          
        case 404:
          toast.error('请求的资源不存在')
          break
          
        case 422:
          // 表单验证错误
          if (data.errors) {
            const firstError = Object.values(data.errors)[0]
            toast.error(Array.isArray(firstError) ? firstError[0] : firstError)
          } else {
            toast.error(data.message || '请求参数错误')
          }
          break
          
        case 429:
          toast.error('请求过于频繁，请稍后再试')
          break
          
        case 500:
          toast.error('服务器内部错误，请稍后重试')
          break
          
        default:
          toast.error(data.message || '请求失败，请重试')
      }
    } else if (error.request) {
      // 网络错误
      toast.error('网络连接失败，请检查网络设置')
    } else {
      // 其他错误
      toast.error('请求失败，请重试')
    }
    
    return Promise.reject(error)
  }
)

export default api
