import { defineStore } from 'pinia'
import { ref } from 'vue'
import api from '@/utils/api'

export const useAgentStore = defineStore('agent', () => {
  // 状态
  const agents = ref([])
  const currentAgent = ref(null)
  const categories = ref([])
  const isLoading = ref(false)
  const pagination = ref({
    current: 1,
    size: 12,
    total: 0,
    pages: 0
  })

  // 获取Agent列表
  const fetchAgents = async (params = {}) => {
    isLoading.value = true
    try {
      const response = await api.get('/api/v1/agent/list', { params })
      
      if (response.data) {
        agents.value = response.data.records || []
        pagination.value = {
          current: response.data.current || 1,
          size: response.data.size || 12,
          total: response.data.total || 0,
          pages: response.data.pages || 0
        }
      }
      
      return { success: true, data: response.data }
    } catch (error) {
      console.error('获取Agent列表失败:', error)
      return { success: false, message: '获取Agent列表失败' }
    } finally {
      isLoading.value = false
    }
  }

  // 获取Agent详情
  const fetchAgentDetail = async (id) => {
    isLoading.value = true
    try {
      const response = await api.get(`/api/v1/agent/${id}`)
      
      if (response.data) {
        currentAgent.value = response.data
        return { success: true, data: response.data }
      }
      
      return { success: false, message: 'Agent不存在' }
    } catch (error) {
      console.error('获取Agent详情失败:', error)
      return { success: false, message: '获取Agent详情失败' }
    } finally {
      isLoading.value = false
    }
  }

  // 获取分类列表
  const fetchCategories = async () => {
    try {
      const response = await api.get('/api/v1/agent/categories')
      categories.value = response.data || []
      return { success: true, data: response.data }
    } catch (error) {
      console.error('获取分类列表失败:', error)
      return { success: false, message: '获取分类列表失败' }
    }
  }

  // 订阅/取消订阅Agent
  const toggleSubscription = async (agentId) => {
    try {
      const response = await api.post(`/api/v1/agent/${agentId}/toggle-subscription`)
      
      if (response.data.success) {
        // 更新本地状态
        const agent = agents.value.find(a => a.id === agentId)
        if (agent) {
          agent.isSubscribed = response.data.isSubscribed
          agent.subscriptionCount = response.data.subscriptionCount
        }
        
        if (currentAgent.value && currentAgent.value.id === agentId) {
          currentAgent.value.isSubscribed = response.data.isSubscribed
          currentAgent.value.subscriptionCount = response.data.subscriptionCount
        }
        
        return { 
          success: true, 
          isSubscribed: response.data.isSubscribed,
          subscriptionCount: response.data.subscriptionCount
        }
      }
      
      return { success: false, message: response.data.message }
    } catch (error) {
      console.error('订阅操作失败:', error)
      return { success: false, message: '订阅操作失败' }
    }
  }

  // 检查订阅状态
  const checkSubscriptionStatus = async (agentId) => {
    try {
      const response = await api.get(`/api/v1/agent/${agentId}`)
      return { 
        success: true, 
        isSubscribed: response.data.isSubscribed 
      }
    } catch (error) {
      console.error('检查订阅状态失败:', error)
      return { success: false, isSubscribed: false }
    }
  }

  // 重置状态
  const resetState = () => {
    agents.value = []
    currentAgent.value = null
    pagination.value = {
      current: 1,
      size: 12,
      total: 0,
      pages: 0
    }
  }

  return {
    agents,
    currentAgent,
    categories,
    isLoading,
    pagination,
    fetchAgents,
    fetchAgentDetail,
    fetchCategories,
    toggleSubscription,
    checkSubscriptionStatus,
    resetState
  }
})
