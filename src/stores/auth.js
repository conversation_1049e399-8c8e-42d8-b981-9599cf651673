import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import api from '@/utils/api'

export const useAuthStore = defineStore('auth', () => {
  // 状态
  const user = ref(null)
  const token = ref(localStorage.getItem('token') || null)
  const isLoading = ref(false)
  const isInitialized = ref(false) // 标记是否已完成初始化

  // 计算属性
  const isAuthenticated = computed(() => !!user.value && !!token.value)

  // 设置Token到请求头
  const setAuthHeader = (authToken) => {
    if (authToken) {
      api.defaults.headers.common['Authorization'] = `Bearer ${authToken}`
    } else {
      delete api.defaults.headers.common['Authorization']
    }
  }

  // 初始化用户信息 - 检查Token状态
  const initUser = async () => {
    try {
      if (!token.value) {
        isInitialized.value = true
        return false
      }

      // 设置请求头
      setAuthHeader(token.value)

      const response = await api.get('/api/v1/vue-auth/user-info')
      if (response.data.success) {
        user.value = response.data.data
        isInitialized.value = true
        return true
      } else {
        logout()
        isInitialized.value = true
        return false
      }
    } catch (error) {
      console.error('获取用户信息失败:', error)
      logout()
      isInitialized.value = true
      return false
    }
  }

  // 检查Token状态
  const checkToken = async () => {
    try {
      if (!token.value) {
        return false
      }

      setAuthHeader(token.value)
      const response = await api.get('/api/v1/vue-auth/check-token')
      return response.data.success && response.data.data.isValid
    } catch (error) {
      console.error('检查Token状态失败:', error)
      return false
    }
  }

  // 登录
  const login = async (email, verificationCode) => {
    isLoading.value = true
    try {
      const response = await api.post('/api/v1/vue-auth/login', {
        email,
        verificationCode
      })

      if (response.data.success) {
        const { user: userData, token: authToken } = response.data.data
        user.value = userData
        token.value = authToken

        // 保存Token到localStorage
        localStorage.setItem('token', authToken)

        // 设置请求头
        setAuthHeader(authToken)

        console.log('登录成功，用户已设置:', user.value?.email, '认证状态:', isAuthenticated.value)
        return { success: true }
      } else {
        return { success: false, message: response.data.message }
      }
    } catch (error) {
      console.error('登录失败:', error)
      return {
        success: false,
        message: error.response?.data?.message || error.message || '登录失败，请重试'
      }
    } finally {
      isLoading.value = false
    }
  }

  // 发送验证码
  const sendVerificationCode = async (email) => {
    try {
      const response = await api.post('/api/v1/vue-auth/send-code', { email })
      return {
        success: response.data.success,
        message: response.data.message
      }
    } catch (error) {
      console.error('发送验证码失败:', error)
      return {
        success: false,
        message: error.response?.data?.message || error.message || '发送验证码失败'
      }
    }
  }

  // 登出
  const logout = async () => {
    try {
      // 调用后端登出API
      if (token.value) {
        setAuthHeader(token.value)
        await api.post('/api/v1/vue-auth/vue-logout')
      }
    } catch (error) {
      console.error('登出请求失败:', error)
    } finally {
      // 清除本地状态
      user.value = null
      token.value = null
      isInitialized.value = false
      localStorage.removeItem('token')

      // 清除请求头
      setAuthHeader(null)
    }
  }

  // 更新用户信息
  const updateUser = (userData) => {
    user.value = { ...user.value, ...userData }
  }

  // 初始化Token（应用启动时调用）
  const initToken = () => {
    if (token.value) {
      setAuthHeader(token.value)
    }
  }

  return {
    user,
    token,
    isLoading,
    isInitialized,
    isAuthenticated,
    initUser,
    checkToken,
    login,
    sendVerificationCode,
    logout,
    updateUser,
    initToken
  }
})
