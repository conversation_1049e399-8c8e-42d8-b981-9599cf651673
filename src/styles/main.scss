// 变量定义 - 与HTML版本保持一致
:root {
  --kt-primary: #009ef7;
  --kt-primary-light: #4facfe;
  --kt-primary-dark: #0085d1;
  --kt-secondary: #e4e6ea;
  --kt-success: #50cd89;
  --kt-info: #7239ea;
  --kt-warning: #ffc700;
  --kt-danger: #f1416c;
  --kt-light: #f5f8fa;
  --kt-dark: #181c32;

  // Header相关变量
  --kt-header-height: 70px;
  --kt-header-bg: #181c32;
  --kt-header-border-color: #2b3445;

  // 渐变色定义
  --kt-gradient-primary: linear-gradient(135deg, #009ef7 0%, #7239ea 100%);
  --kt-gradient-success: linear-gradient(135deg, var(--kt-success) 0%, #20c997 100%);
  --kt-gradient-warning: linear-gradient(135deg, var(--kt-warning) 0%, #fd7e14 100%);
  --kt-gradient-danger: linear-gradient(135deg, var(--kt-danger) 0%, #e83e8c 100%);

  --kt-border-radius: 0.625rem;
  --kt-border-radius-sm: 0.475rem;
  --kt-box-shadow: 0 0.5rem 1.5rem 0.5rem rgba(0, 0, 0, 0.075);
  --kt-box-shadow-lg: 0 1rem 3rem rgba(0, 0, 0, 0.175);

  // 文本颜色
  --kt-text-dark: #1e293b;
  --kt-text-muted: #64748b;
  --kt-text-gray-700: #334155;
}

// 全局样式
* {
  box-sizing: border-box;
}

html, body {
  height: 100%;
  margin: 0;
  padding: 0;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  line-height: 1.6;
  color: #333;
  background-color: #f8f9fa;
}

#app {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

// 主内容区域样式 - 与HTML版本保持一致
.main-content {
  margin-top: var(--kt-header-height);
  background-color: #f8fafc;
  padding-bottom: 2rem;
  flex: 1;
}

// 通用工具类
.min-vh-75 {
  min-height: 75vh;
}

.min-vh-100 {
  min-height: 100vh;
}

.text-gradient {
  background: var(--kt-gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.bg-gradient-primary {
  background: var(--kt-gradient-primary);
}

.bg-gradient-success {
  background: var(--kt-gradient-success);
}

.bg-gradient-warning {
  background: var(--kt-gradient-warning);
}

.bg-gradient-danger {
  background: var(--kt-gradient-danger);
}

// 卡片样式
.kt-card {
  background: white;
  border-radius: var(--kt-border-radius);
  box-shadow: var(--kt-box-shadow);
  border: 1px solid rgba(0, 0, 0, 0.125);
  margin-bottom: 1.5rem;
  
  .kt-card-header {
    padding: 1.25rem;
    border-bottom: 1px solid rgba(0, 0, 0, 0.125);
    background-color: rgba(0, 0, 0, 0.03);
    border-radius: var(--kt-border-radius) var(--kt-border-radius) 0 0;
  }
  
  .kt-card-body {
    padding: 1.25rem;
  }
  
  .kt-card-footer {
    padding: 1.25rem;
    border-top: 1px solid rgba(0, 0, 0, 0.125);
    background-color: rgba(0, 0, 0, 0.03);
    border-radius: 0 0 var(--kt-border-radius) var(--kt-border-radius);
  }
}

// 按钮样式增强
.btn {
  border-radius: var(--kt-border-radius);
  font-weight: 500;
  transition: all 0.3s ease;
  
  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
  }
  
  &.btn-gradient-primary {
    background: var(--kt-gradient-primary);
    border: none;
    color: white;
  }
  
  &.btn-gradient-success {
    background: var(--kt-gradient-success);
    border: none;
    color: white;
  }
}

// 表单样式
.form-control {
  border-radius: var(--kt-border-radius);
  border: 1px solid #ddd;
  transition: all 0.3s ease;
  
  &:focus {
    border-color: var(--kt-primary);
    box-shadow: 0 0 0 0.2rem rgba(30, 136, 229, 0.25);
  }
}

// 加载动画
.loading-spinner {
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 3px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: #fff;
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

// 淡入动画
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

// 滑动动画
.slide-enter-active,
.slide-leave-active {
  transition: all 0.3s ease;
}

.slide-enter-from {
  transform: translateX(-100%);
}

.slide-leave-to {
  transform: translateX(100%);
}

// 响应式工具类
@media (max-width: 768px) {
  .container {
    padding-left: 15px;
    padding-right: 15px;
  }
  
  .kt-card {
    margin-bottom: 1rem;
    
    .kt-card-header,
    .kt-card-body,
    .kt-card-footer {
      padding: 1rem;
    }
  }
}

// 滚动条样式
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
  
  &:hover {
    background: #a8a8a8;
  }
}

// 文本选择样式
::selection {
  background-color: var(--kt-primary);
  color: white;
}
