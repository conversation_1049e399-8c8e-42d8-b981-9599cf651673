<template>
  <div class="privacy-page">
    <Header />
    
    <main class="main-content">
      <div class="container py-5">
        <!-- 页面标题 -->
        <div class="row mb-5">
          <div class="col-12">
            <div class="text-center">
              <h1 class="display-5 fw-bold text-primary mb-3">
                <i class="fas fa-shield-alt me-3"></i>
                隐私政策
              </h1>
              <p class="lead text-muted">
                智能体矩阵平台致力于保护您的个人信息和隐私安全
              </p>
              <div class="text-muted">
                <small>最后更新时间：{{ lastUpdated }}</small>
              </div>
            </div>
          </div>
        </div>

        <!-- 政策内容 -->
        <div class="row justify-content-center">
          <div class="col-lg-10 col-xl-8">
            <div class="card shadow-sm">
              <div class="card-body p-5">
                
                <!-- 概述 -->
                <section class="mb-5">
                  <h2 class="h4 text-primary mb-3">
                    <i class="fas fa-info-circle me-2"></i>
                    概述
                  </h2>
                  <p class="text-muted lh-lg">
                    智能体矩阵平台（以下简称"我们"或"平台"）深知个人信息对您的重要性，并会尽全力保护您的个人信息安全可靠。我们致力于维持您对我们的信任，恪守以下原则，保护您的个人信息：权责一致原则、目的明确原则、选择同意原则、最少够用原则、确保安全原则、主体参与原则、公开透明原则等。
                  </p>
                </section>

                <!-- 信息收集 -->
                <section class="mb-5">
                  <h2 class="h4 text-primary mb-3">
                    <i class="fas fa-database me-2"></i>
                    我们收集的信息
                  </h2>
                  <div class="row">
                    <div class="col-md-6 mb-3">
                      <div class="border-start border-primary border-3 ps-3">
                        <h5 class="fw-semibold">账户信息</h5>
                        <ul class="text-muted">
                          <li>用户名和邮箱地址</li>
                          <li>登录凭证和验证信息</li>
                          <li>账户设置和偏好</li>
                        </ul>
                      </div>
                    </div>
                    <div class="col-md-6 mb-3">
                      <div class="border-start border-success border-3 ps-3">
                        <h5 class="fw-semibold">使用数据</h5>
                        <ul class="text-muted">
                          <li>API调用记录和频次</li>
                          <li>服务使用统计</li>
                          <li>错误日志和性能数据</li>
                        </ul>
                      </div>
                    </div>
                  </div>
                </section>

                <!-- 信息使用 -->
                <section class="mb-5">
                  <h2 class="h4 text-primary mb-3">
                    <i class="fas fa-cogs me-2"></i>
                    信息使用方式
                  </h2>
                  <div class="alert alert-light border-start border-info border-3">
                    <p class="mb-2"><strong>我们使用收集的信息用于：</strong></p>
                    <ul class="mb-0 text-muted">
                      <li>提供、维护和改进我们的服务</li>
                      <li>处理交易和发送相关通知</li>
                      <li>响应您的评论、问题和客户服务请求</li>
                      <li>发送技术通知、更新、安全警报和支持消息</li>
                      <li>监控和分析使用趋势以改善用户体验</li>
                    </ul>
                  </div>
                </section>

                <!-- 信息保护 -->
                <section class="mb-5">
                  <h2 class="h4 text-primary mb-3">
                    <i class="fas fa-lock me-2"></i>
                    信息保护措施
                  </h2>
                  <div class="row g-4">
                    <div class="col-md-4">
                      <div class="text-center p-3 bg-light rounded">
                        <i class="fas fa-encrypt fa-2x text-primary mb-2"></i>
                        <h6 class="fw-semibold">数据加密</h6>
                        <small class="text-muted">采用行业标准加密技术保护数据传输和存储</small>
                      </div>
                    </div>
                    <div class="col-md-4">
                      <div class="text-center p-3 bg-light rounded">
                        <i class="fas fa-user-shield fa-2x text-success mb-2"></i>
                        <h6 class="fw-semibold">访问控制</h6>
                        <small class="text-muted">严格限制员工对个人信息的访问权限</small>
                      </div>
                    </div>
                    <div class="col-md-4">
                      <div class="text-center p-3 bg-light rounded">
                        <i class="fas fa-server fa-2x text-warning mb-2"></i>
                        <h6 class="fw-semibold">安全存储</h6>
                        <small class="text-muted">使用安全的服务器和数据中心存储信息</small>
                      </div>
                    </div>
                  </div>
                </section>

                <!-- 用户权利 -->
                <section class="mb-5">
                  <h2 class="h4 text-primary mb-3">
                    <i class="fas fa-user-check me-2"></i>
                    您的权利
                  </h2>
                  <div class="bg-light p-4 rounded">
                    <p class="fw-semibold mb-3">您对个人信息享有以下权利：</p>
                    <div class="row">
                      <div class="col-md-6">
                        <ul class="list-unstyled">
                          <li class="mb-2">
                            <i class="fas fa-check text-success me-2"></i>
                            <strong>访问权：</strong>查看我们持有的您的个人信息
                          </li>
                          <li class="mb-2">
                            <i class="fas fa-check text-success me-2"></i>
                            <strong>更正权：</strong>要求更正不准确的个人信息
                          </li>
                          <li class="mb-2">
                            <i class="fas fa-check text-success me-2"></i>
                            <strong>删除权：</strong>要求删除您的个人信息
                          </li>
                        </ul>
                      </div>
                      <div class="col-md-6">
                        <ul class="list-unstyled">
                          <li class="mb-2">
                            <i class="fas fa-check text-success me-2"></i>
                            <strong>限制权：</strong>限制我们处理您的个人信息
                          </li>
                          <li class="mb-2">
                            <i class="fas fa-check text-success me-2"></i>
                            <strong>携带权：</strong>获取您的个人信息副本
                          </li>
                          <li class="mb-2">
                            <i class="fas fa-check text-success me-2"></i>
                            <strong>反对权：</strong>反对我们处理您的个人信息
                          </li>
                        </ul>
                      </div>
                    </div>
                  </div>
                </section>



              </div>
            </div>
          </div>
        </div>
      </div>
    </main>
    
    <Footer />
  </div>
</template>

<script setup>
import { computed } from 'vue'
import Header from '@/components/Header.vue'
import Footer from '@/components/Footer.vue'

// 最后更新时间
const lastUpdated = computed(() => {
  return new Date().toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  })
})

// 设置页面标题
document.title = '隐私政策 - 智能体矩阵平台'
</script>

<style scoped>
.privacy-page {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f8f9fa;
}

.main-content {
  flex: 1;
  min-height: calc(100vh - 140px);
}

.card {
  border: none;
  border-radius: 15px;
}

.border-3 {
  border-width: 3px !important;
}

.lh-lg {
  line-height: 1.8;
}

/* 深色主题适配 */
.theme-dark .privacy-page {
  background: #1e1e2d;
}

.theme-dark .card {
  background: #2b2b40;
  color: #ffffff;
}

.theme-dark .text-muted {
  color: #a1a5b7 !important;
}

.theme-dark .bg-light {
  background: #3f3f56 !important;
}

.theme-dark .alert-light {
  background: #3f3f56 !important;
  border-color: #009EF7 !important;
  color: #ffffff;
}

.theme-dark .alert-primary {
  background: rgba(0, 158, 247, 0.1) !important;
  border-color: #009EF7 !important;
  color: #ffffff;
}
</style>
