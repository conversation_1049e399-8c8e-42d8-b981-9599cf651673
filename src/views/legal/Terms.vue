<template>
  <div class="terms-page">
    <Header />
    
    <main class="main-content">
      <div class="container py-5">
        <!-- 页面标题 -->
        <div class="row mb-5">
          <div class="col-12">
            <div class="text-center">
              <h1 class="display-5 fw-bold text-primary mb-3">
                <i class="fas fa-file-contract me-3"></i>
                服务条款
              </h1>
              <p class="lead text-muted">
                使用智能体矩阵平台服务前，请仔细阅读并同意以下条款
              </p>
              <div class="text-muted">
                <small>最后更新时间：{{ lastUpdated }}</small>
              </div>
            </div>
          </div>
        </div>

        <!-- 条款内容 -->
        <div class="row justify-content-center">
          <div class="col-lg-10 col-xl-8">
            <div class="card shadow-sm">
              <div class="card-body p-5">
                
                <!-- 接受条款 -->
                <section class="mb-5">
                  <h2 class="h4 text-primary mb-3">
                    <i class="fas fa-handshake me-2"></i>
                    接受条款
                  </h2>
                  <div class="alert alert-info border-start border-info border-3">
                    <p class="mb-0 lh-lg">
                      欢迎使用智能体矩阵平台！通过访问或使用我们的服务，您同意受本服务条款的约束。如果您不同意这些条款，请不要使用我们的服务。我们可能会不时更新这些条款，更新后的条款将在发布后立即生效。
                    </p>
                  </div>
                </section>

                <!-- 服务描述 -->
                <section class="mb-5">
                  <h2 class="h4 text-primary mb-3">
                    <i class="fas fa-robot me-2"></i>
                    服务描述
                  </h2>
                  <p class="text-muted lh-lg mb-4">
                    智能体矩阵平台是一个专业的智能文档识别与自动填写平台，为用户提供以下服务：
                  </p>
                  <div class="row g-4">
                    <div class="col-md-6">
                      <div class="d-flex">
                        <div class="flex-shrink-0">
                          <i class="fas fa-check-circle text-success fa-lg"></i>
                        </div>
                        <div class="flex-grow-1 ms-3">
                          <h6 class="fw-semibold">AI智能识别</h6>
                          <p class="text-muted small mb-0">提供高精度的文档识别和数据提取服务</p>
                        </div>
                      </div>
                    </div>
                    <div class="col-md-6">
                      <div class="d-flex">
                        <div class="flex-shrink-0">
                          <i class="fas fa-check-circle text-success fa-lg"></i>
                        </div>
                        <div class="flex-grow-1 ms-3">
                          <h6 class="fw-semibold">自动填写</h6>
                          <p class="text-muted small mb-0">智能表单填写和数据处理功能</p>
                        </div>
                      </div>
                    </div>
                    <div class="col-md-6">
                      <div class="d-flex">
                        <div class="flex-shrink-0">
                          <i class="fas fa-check-circle text-success fa-lg"></i>
                        </div>
                        <div class="flex-grow-1 ms-3">
                          <h6 class="fw-semibold">API接口</h6>
                          <p class="text-muted small mb-0">开放的API接口供开发者集成使用</p>
                        </div>
                      </div>
                    </div>
                    <div class="col-md-6">
                      <div class="d-flex">
                        <div class="flex-shrink-0">
                          <i class="fas fa-check-circle text-success fa-lg"></i>
                        </div>
                        <div class="flex-grow-1 ms-3">
                          <h6 class="fw-semibold">数据管理</h6>
                          <p class="text-muted small mb-0">安全的数据存储和管理服务</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </section>

                <!-- 用户责任 -->
                <section class="mb-5">
                  <h2 class="h4 text-primary mb-3">
                    <i class="fas fa-user-cog me-2"></i>
                    用户责任
                  </h2>
                  <div class="bg-light p-4 rounded">
                    <p class="fw-semibold mb-3">使用我们的服务时，您同意：</p>
                    <ul class="text-muted">
                      <li class="mb-2">提供准确、完整的注册信息并及时更新</li>
                      <li class="mb-2">保护您的账户安全，不与他人共享登录凭证</li>
                      <li class="mb-2">遵守所有适用的法律法规和本服务条款</li>
                      <li class="mb-2">不得滥用服务或进行任何可能损害平台的行为</li>
                      <li class="mb-2">尊重他人的知识产权和隐私权</li>
                      <li class="mb-0">对您通过服务上传或处理的内容承担全部责任</li>
                    </ul>
                  </div>
                </section>

                <!-- 使用限制 -->
                <section class="mb-5">
                  <h2 class="h4 text-primary mb-3">
                    <i class="fas fa-ban me-2"></i>
                    使用限制
                  </h2>
                  <div class="alert alert-warning border-start border-warning border-3">
                    <p class="fw-semibold mb-2">禁止以下行为：</p>
                    <div class="row">
                      <div class="col-md-6">
                        <ul class="small text-muted">
                          <li>恶意攻击或破坏系统</li>
                          <li>传播病毒或恶意代码</li>
                          <li>侵犯他人知识产权</li>
                          <li>发布违法或有害内容</li>
                        </ul>
                      </div>
                      <div class="col-md-6">
                        <ul class="small text-muted">
                          <li>绕过技术限制或安全措施</li>
                          <li>进行商业间谍或竞争情报收集</li>
                          <li>批量注册账户或自动化操作</li>
                          <li>其他违反法律法规的行为</li>
                        </ul>
                      </div>
                    </div>
                  </div>
                </section>

                <!-- 知识产权 -->
                <section class="mb-5">
                  <h2 class="h4 text-primary mb-3">
                    <i class="fas fa-copyright me-2"></i>
                    知识产权
                  </h2>
                  <div class="border-start border-primary border-3 ps-4">
                    <p class="text-muted lh-lg">
                      智能体矩阵平台及其所有内容、功能和服务均受版权、商标和其他知识产权法律保护。除非明确授权，否则您不得复制、修改、分发、销售或租赁我们服务的任何部分。
                    </p>
                  </div>
                </section>

                <!-- 免责声明 -->
                <section class="mb-5">
                  <h2 class="h4 text-primary mb-3">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    免责声明
                  </h2>
                  <div class="alert alert-light border">
                    <p class="small text-muted mb-2">
                      <strong>服务按"现状"提供：</strong>我们不保证服务的连续性、准确性或完整性。
                    </p>
                    <p class="small text-muted mb-2">
                      <strong>责任限制：</strong>在法律允许的最大范围内，我们不承担因使用服务而产生的任何间接、偶然或后果性损害。
                    </p>
                    <p class="small text-muted mb-0">
                      <strong>第三方内容：</strong>我们不对第三方内容或服务的准确性、合法性或适用性承担责任。
                    </p>
                  </div>
                </section>

                <!-- 服务变更和终止 -->
                <section class="mb-5">
                  <h2 class="h4 text-primary mb-3">
                    <i class="fas fa-sync-alt me-2"></i>
                    服务变更和终止
                  </h2>
                  <p class="text-muted lh-lg">
                    我们保留随时修改、暂停或终止服务的权利，恕不另行通知。我们也可能因违反本条款而终止您的账户。在终止时，您使用服务的权利将立即停止。
                  </p>
                </section>



              </div>
            </div>
          </div>
        </div>
      </div>
    </main>
    
    <Footer />
  </div>
</template>

<script setup>
import { computed } from 'vue'
import Header from '@/components/Header.vue'
import Footer from '@/components/Footer.vue'

// 最后更新时间
const lastUpdated = computed(() => {
  return new Date().toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  })
})

// 设置页面标题
document.title = '服务条款 - 智能体矩阵平台'
</script>

<style scoped>
.terms-page {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f8f9fa;
}

.main-content {
  flex: 1;
  min-height: calc(100vh - 140px);
}

.card {
  border: none;
  border-radius: 15px;
}

.border-3 {
  border-width: 3px !important;
}

.lh-lg {
  line-height: 1.8;
}

/* 深色主题适配 */
.theme-dark .terms-page {
  background: #1e1e2d;
}

.theme-dark .card {
  background: #2b2b40;
  color: #ffffff;
}

.theme-dark .text-muted {
  color: #a1a5b7 !important;
}

.theme-dark .bg-light {
  background: #3f3f56 !important;
}

.theme-dark .alert-light {
  background: #3f3f56 !important;
  border-color: #6c757d !important;
  color: #ffffff;
}

.theme-dark .alert-info {
  background: rgba(0, 158, 247, 0.1) !important;
  border-color: #009EF7 !important;
  color: #ffffff;
}

.theme-dark .alert-warning {
  background: rgba(255, 199, 0, 0.1) !important;
  border-color: #FFC700 !important;
  color: #ffffff;
}

.theme-dark .alert-primary {
  background: rgba(0, 158, 247, 0.1) !important;
  border-color: #009EF7 !important;
  color: #ffffff;
}
</style>
