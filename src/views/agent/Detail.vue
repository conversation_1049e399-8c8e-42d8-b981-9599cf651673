<template>
  <div class="agent-detail-page">
    <Header />
    
    <main>
      <!-- 页面头部区域 -->
      <div class="page-header">
        <div class="container">
          <div v-if="loading" class="text-center py-5">
            <div class="spinner-border text-white" role="status">
              <span class="visually-hidden">加载中...</span>
            </div>
            <p class="text-white mt-3">加载Agent详情...</p>
          </div>
          
          <div v-else-if="!agent" class="text-center py-5">
            <i class="fas fa-exclamation-triangle fa-4x text-warning mb-4"></i>
            <h3 class="text-white">Agent不存在</h3>
            <p class="text-white-50 mb-4">您访问的Agent可能已被删除或不存在</p>
            <router-link to="/agent/square" class="btn btn-light">
              <i class="fas fa-arrow-left me-2"></i>
              返回Agent广场
            </router-link>
          </div>
          
          <div v-else class="row align-items-center">
            <div class="col-lg-8">
              <div class="d-flex align-items-start mb-4">
                <div class="agent-icon me-4">
                  <i class="fas fa-robot"></i>
                </div>
                <div class="flex-grow-1">
                  <div class="mb-2">
                    <span class="badge text-white px-3 py-2 agent-code-badge">
                      编码: {{ agent.agentCode }}
                    </span>
                  </div>
                  <h1 class="display-4 mb-3 fw-bold text-white">
                    {{ agent.agentName }}
                  </h1>
                  <p class="lead mb-0 opacity-90 text-white">
                    {{ agent.description }}
                  </p>
                </div>
              </div>

              <div class="mb-0">
                <span class="badge bg-white text-primary me-2 mb-2 px-3 py-2">
                  {{ agent.categoryText }}
                </span>
                <span class="badge bg-white text-primary me-2 mb-2 px-3 py-2">
                  {{ agent.typeText }}
                </span>
                <span class="badge bg-white text-primary me-2 mb-2 px-3 py-2">
                  v{{ agent.version }}
                </span>
                <span v-if="agent.isRecommended === 1"
                      class="badge bg-warning text-dark me-2 mb-2 px-3 py-2">
                  <i class="fas fa-fire me-1"></i>HOT
                </span>
              </div>
            </div>

            <div class="col-lg-4">
              <div class="kt-card agent-info-card">
                <div class="kt-card-body">
                  <!-- 订阅按钮 -->
                  <div class="d-flex justify-content-center mb-4">
                    <button 
                      class="btn btn-lg px-4"
                      :class="isSubscribed ? 'btn-subscribe-active' : 'btn-subscribe-inactive'"
                      @click="toggleSubscription"
                      :disabled="subscribing">
                      <i :class="isSubscribed ? 'fas fa-heart-broken me-2' : 'fas fa-heart me-2'"></i>
                      <span>{{ isSubscribed ? '取消订阅' : '订阅' }}</span>
                    </button>
                  </div>

                  <!-- 使用统计 -->
                  <div class="text-center">
                    <div class="stat-item">
                      <div class="stat-value h2 mb-1">
                        <span>{{ formatNumber(agent.usageCount || 0) }}</span>
                      </div>
                      <div class="stat-label small opacity-75">总使用次数</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 主要内容区域 -->
      <div class="container-fluid" v-if="agent">
        <div class="row">
          <!-- 左侧主要内容 -->
          <div class="col-lg-8 col-md-12">
            <!-- 详细介绍 -->
            <div class="kt-card">
              <div class="kt-card-header">
                <h3 class="mb-0">
                  <i class="fas fa-info-circle me-2 text-primary"></i>
                  详细介绍
                </h3>
                <p class="text-muted mb-0 mt-2">了解该Agent的核心功能和技术特点</p>
              </div>
              <div class="kt-card-body">
                <div class="content-text">
                  <div v-if="agent.agentIntroduction" v-html="agent.agentIntroduction"></div>
                  <div v-else>{{ agent.description || '暂无描述' }}</div>
                </div>
              </div>
            </div>

            <!-- 产品截图轮播 -->
            <div class="kt-card">
              <div class="kt-card-header">
                <h3 class="mb-0">
                  <i class="fas fa-images me-2 text-primary"></i>
                  产品截图
                </h3>
                <p class="text-muted mb-0 mt-2">查看Agent的实际使用界面和功能展示</p>
              </div>
              <div class="kt-card-body">

                <div v-if="screenshots.length > 0" id="screenshotCarousel" class="carousel slide screenshot-carousel" data-bs-ride="carousel" data-bs-interval="4000">
                  <!-- 轮播指示器 -->
                  <div class="carousel-indicators" v-if="screenshots.length > 1">
                    <button
                      v-for="(screenshot, index) in screenshots"
                      :key="`indicator-${index}`"
                      type="button"
                      data-bs-target="#screenshotCarousel"
                      :data-bs-slide-to="index"
                      :class="{ active: index === 0 }"
                      :aria-label="`截图 ${index + 1}`">
                    </button>
                  </div>

                  <div class="carousel-inner">
                    <div
                      v-for="(screenshot, index) in screenshots"
                      :key="index"
                      class="carousel-item"
                      :class="{ active: index === 0 }">
                      <div class="screenshot-container">
                        <img :src="screenshot" class="d-block w-100 screenshot-image" :alt="`截图 ${index + 1}`" loading="lazy">
                      </div>
                    </div>
                  </div>

                  <!-- 控制按钮 -->
                  <button v-if="screenshots.length > 1" class="carousel-control-prev" type="button" data-bs-target="#screenshotCarousel" data-bs-slide="prev">
                    <span class="carousel-control-prev-icon" aria-hidden="true"></span>
                    <span class="visually-hidden">上一张</span>
                  </button>
                  <button v-if="screenshots.length > 1" class="carousel-control-next" type="button" data-bs-target="#screenshotCarousel" data-bs-slide="next">
                    <span class="carousel-control-next-icon" aria-hidden="true"></span>
                    <span class="visually-hidden">下一张</span>
                  </button>
                </div>
                <div v-else class="text-center py-4">
                  <i class="fas fa-image fa-3x text-muted mb-3"></i>
                  <p class="text-muted">暂无产品截图</p>
                </div>
              </div>
            </div>

            <!-- 使用场景说明 -->
            <div class="kt-card">
              <div class="kt-card-header">
                <h3 class="mb-0">
                  <i class="fas fa-lightbulb me-2 text-warning"></i>
                  使用场景说明
                </h3>
                <p class="text-muted mb-0 mt-2">了解该Agent在实际工作中的应用场景和使用方法</p>
              </div>
              <div class="kt-card-body">
                <div class="content-text" v-html="agent.usageScenarios || '暂无使用场景说明'"></div>
              </div>
            </div>
          </div>

          <!-- 右侧边栏 -->
          <div class="col-lg-4 col-md-12">
            <!-- Agent信息 -->
            <div class="kt-card">
              <div class="kt-card-header">
                <h5 class="mb-0">
                  <i class="fas fa-info-circle me-2 text-primary"></i>
                  Agent信息
                </h5>
              </div>
              <div class="kt-card-body">
                <div class="row text-center">
                  <div class="col-6 mb-4">
                    <div class="stat-item">
                      <div class="stat-value h4 mb-1 text-success">
                        <span class="animated-number">{{ agent.goodRating || 0 }}</span>
                      </div>
                      <div class="stat-label small text-muted">
                        <i class="fas fa-thumbs-up me-1 text-success"></i>好评数
                      </div>
                    </div>
                  </div>
                  <div class="col-6 mb-4">
                    <div class="stat-item">
                      <div class="stat-value h4 mb-1 text-danger">
                        <span class="animated-number">{{ agent.badRating || 0 }}</span>
                      </div>
                      <div class="stat-label small text-muted">
                        <i class="fas fa-thumbs-down me-1 text-danger"></i>差评数
                      </div>
                    </div>
                  </div>
                  <div class="col-6">
                    <div class="stat-item">
                      <div class="stat-value h4 mb-1 text-primary">
                        <span class="animated-number">{{ subscriptionCount }}</span>
                      </div>
                      <div class="stat-label small text-muted">
                        <i class="fas fa-heart me-1 text-primary"></i>订阅人数
                      </div>
                    </div>
                  </div>
                  <div class="col-6">
                    <div class="stat-item">
                      <div class="stat-value h4 mb-1 text-success">
                        <i class="fas fa-circle text-success animated-pulse" style="font-size: 0.8rem;"></i>
                      </div>
                      <div class="stat-label small text-muted">
                        <i class="fas fa-server me-1 text-success"></i>服务状态
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 相关推荐 -->
            <div v-if="relatedAgents.length > 0" class="kt-card">
              <div class="kt-card-header">
                <h5 class="mb-0">
                  <i class="fas fa-star me-2 text-warning"></i>
                  相关推荐
                </h5>
              </div>
              <div class="kt-card-body">
                <div 
                  v-for="relatedAgent in relatedAgents" 
                  :key="relatedAgent.id"
                  class="related-agent-item mb-3 p-3 border rounded">
                  <div class="d-flex align-items-center">
                    <div class="agent-icon-small me-3">
                      <i class="fas fa-robot"></i>
                    </div>
                    <div class="flex-grow-1">
                      <h6 class="mb-1">
                        <router-link 
                          :to="`/agent/detail/${relatedAgent.id}`" 
                          class="text-decoration-none">
                          {{ relatedAgent.agentName }}
                        </router-link>
                      </h6>
                      <p class="text-muted small mb-0">{{ relatedAgent.description }}</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </main>

    <Footer />
  </div>
</template>

<script setup>
import { ref, onMounted, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useToast } from 'vue-toastification'
import Header from '@/components/Header.vue'
import Footer from '@/components/Footer.vue'
import api from '@/utils/api'
import { getApiBaseUrl } from '@/utils/env'

const route = useRoute()
const router = useRouter()
const toast = useToast()

// 响应式数据
const loading = ref(true)
const subscribing = ref(false)
const agent = ref(null)
const isSubscribed = ref(false)
const subscriptionCount = ref(0)
const relatedAgents = ref([])
const screenshots = ref([])

// 轮播图控制
const initCarousel = () => {
  // 等待DOM更新后初始化轮播图
  setTimeout(() => {
    const carouselElement = document.getElementById('screenshotCarousel')
    if (carouselElement && screenshots.value.length > 1) {
      // 检查Bootstrap是否可用
      if (typeof window.bootstrap === 'undefined') {
        console.warn('Bootstrap未加载，尝试手动实现轮播')
        startManualCarousel(carouselElement)
        return
      }

      // 初始化Bootstrap轮播组件
      let carousel = window.bootstrap.Carousel.getInstance(carouselElement)
      if (!carousel) {
        carousel = new window.bootstrap.Carousel(carouselElement, {
          interval: 4000,
          ride: 'carousel',
          pause: 'hover',
          wrap: true
        })
      }

      // 确保轮播开始
      carousel.cycle()

      // 鼠标悬停时暂停自动播放
      carouselElement.addEventListener('mouseenter', () => {
        carousel.pause()
      })

      // 鼠标离开时恢复自动播放
      carouselElement.addEventListener('mouseleave', () => {
        carousel.cycle()
      })

      console.log('轮播图初始化成功，图片数量:', screenshots.value.length)
    } else if (carouselElement && screenshots.value.length === 1) {
      console.log('只有一张图片，不需要轮播')
    }
  }, 200)
}

// 手动实现轮播（Bootstrap不可用时的备用方案）
const startManualCarousel = (carouselElement) => {
  let currentIndex = 0
  let intervalId = null
  const items = carouselElement.querySelectorAll('.carousel-item')

  if (items.length <= 1) return

  const nextSlide = () => {
    items[currentIndex].classList.remove('active')
    currentIndex = (currentIndex + 1) % items.length
    items[currentIndex].classList.add('active')

    // 更新指示器
    const indicators = carouselElement.querySelectorAll('.carousel-indicators button')
    indicators.forEach((indicator, index) => {
      indicator.classList.toggle('active', index === currentIndex)
    })
  }

  const startInterval = () => {
    if (intervalId) clearInterval(intervalId)
    intervalId = setInterval(nextSlide, 3000)
  }

  const stopInterval = () => {
    if (intervalId) {
      clearInterval(intervalId)
      intervalId = null
    }
  }

  // 开始自动播放
  startInterval()

  // 鼠标悬停时暂停
  carouselElement.addEventListener('mouseenter', stopInterval)

  // 鼠标离开时恢复
  carouselElement.addEventListener('mouseleave', startInterval)

  console.log('手动轮播初始化成功')
}

// 获取Agent详情
const loadAgentDetail = async () => {
  try {
    loading.value = true
    const agentId = route.params.id

    const response = await api.get(`/api/v1/agent/${agentId}`)
    const data = response.data

    // 后端直接返回AgentSquareDTO对象
    agent.value = data
    isSubscribed.value = data.isSubscribed || false
    subscriptionCount.value = data.subscriptionCount || 0
    relatedAgents.value = [] // 暂时设为空数组，如果需要相关Agent可以单独接口获取

    // 处理截图URLs
    if (agent.value.screenshotUrls && agent.value.screenshotUrls.trim() !== '') {
      try {
        // 如果screenshotUrls是逗号分隔的字符串，先分割
        let urls = []
        if (agent.value.screenshotUrls.startsWith('[')) {
          // JSON数组格式
          urls = JSON.parse(agent.value.screenshotUrls)
        } else {
          // 逗号分隔的字符串格式
          urls = agent.value.screenshotUrls.split(',').map(url => url.trim()).filter(url => url)
        }

        // 将截图URL转换为完整的API地址
        const apiBaseUrl = getApiBaseUrl()
        screenshots.value = urls.map(url => {
          // 如果URL已经是完整的HTTP地址，直接使用
          if (url.startsWith('http://') || url.startsWith('https://')) {
            return url
          }

          // 如果URL以/开头，说明是相对路径，需要拼接API基础URL
          if (url.startsWith('/')) {
            return apiBaseUrl + url
          }

          // 其他情况，也拼接API基础URL
          return apiBaseUrl + '/' + url
        })
      } catch (e) {
        console.error('解析截图URLs失败:', e, 'screenshotUrls:', agent.value.screenshotUrls)
        screenshots.value = []
      }
    } else {
      screenshots.value = []
    }

    // 设置页面标题
    document.title = `${agent.value.agentName} - Agent详情`

    // 初始化轮播图
    initCarousel()
  } catch (error) {
    console.error('获取Agent详情失败:', error)
    toast.error('获取Agent详情失败，请稍后重试')
    router.push('/agent/square')
  } finally {
    loading.value = false
  }
}

// 切换订阅状态
const toggleSubscription = async () => {
  if (!agent.value) return

  try {
    subscribing.value = true

    const response = await api.post(`/api/v1/agent/${agent.value.id}/toggle-subscription`)
    const data = response.data

    if (data.success) {
      isSubscribed.value = data.isSubscribed
      subscriptionCount.value = data.subscriptionCount

      const action = data.isSubscribed ? '订阅' : '取消订阅'
      toast.success(`${action}成功！`)
    } else {
      toast.error(data.message || '操作失败')
    }
  } catch (error) {
    console.error('订阅操作失败:', error)
    toast.error('网络错误，请稍后重试')
  } finally {
    subscribing.value = false
  }
}

// 格式化数字
const formatNumber = (num) => {
  if (num >= 1000000) {
    return (num / 1000000).toFixed(1) + 'M'
  } else if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'K'
  }
  return num.toString()
}

// 页面加载时获取数据
onMounted(() => {
  loadAgentDetail()

  // 延迟初始化轮播图，确保DOM完全渲染
  setTimeout(() => {
    initCarousel()
  }, 500)
})

// 监听路由参数变化，当从一个agent详情页跳转到另一个agent详情页时刷新数据
watch(
  () => route.params.id,
  (newId, oldId) => {
    if (newId && newId !== oldId) {
      console.log('路由参数变化，重新加载Agent详情:', { from: oldId, to: newId })
      loadAgentDetail()
    }
  }
)
</script>

<style scoped>
/* Keen主题基础样式 - 与HTML版本完全一致 */
.agent-detail-page {
  background: #f8f9fa;
  font-family: var(--kt-font-sans-serif, 'Inter', sans-serif);
  color: var(--kt-text-dark, #181C32);
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.main-content {
  flex: 1;
  min-height: calc(100vh - 140px); /* 减去Header和Footer的大概高度 */
  line-height: 1.6;
}

/* 容器样式 */
.container {
  max-width: 1400px !important;
}

@media (min-width: 1600px) {
  .container {
    max-width: 1500px !important;
  }
}

/* Keen主题页面头部样式 */
.page-header {
  background: linear-gradient(135deg, #009ef7 0%, #4facfe 100%);
  color: white;
  padding: 2rem 0;
  margin-bottom: 2rem;
  position: relative;
  overflow: hidden;
}

.page-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 15% 25%, rgba(255, 255, 255, 0.1) 0%, transparent 45%),
              radial-gradient(circle at 85% 75%, rgba(255, 255, 255, 0.08) 0%, transparent 45%);
  opacity: 0.9;
}

.page-header .container {
  position: relative;
  z-index: 3;
}

/* Agent图标样式 - 与HTML版本完全一致 */
.agent-icon {
  width: 80px;
  height: 80px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2.5rem;
  color: white;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  flex-shrink: 0;
}

.agent-icon i {
  font-size: 2.5rem;
  color: white;
}

.agent-icon-small {
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, #009ef7 0%, #4facfe 100%);
  border-radius: 0.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1rem;
  flex-shrink: 0;
}

.agent-icon-small i {
  font-size: 1rem;
  color: white;
}

.agent-code-badge {
  background: rgba(255, 255, 255, 0.15) !important;
  border: 1px solid rgba(255, 255, 255, 0.3);
  font-size: 0.9rem;
  font-weight: 500;
  backdrop-filter: blur(10px);
}

/* Keen主题卡片样式 - 与HTML版本完全一致 */
.kt-card {
  background: #ffffff;
  border: 1px solid #e2e8f0;
  border-radius: 0.5rem;
  box-shadow: 0 0.1rem 0.75rem 0.25rem rgba(0, 0, 0, 0.05);
  transition: all 0.2s ease;
  margin-bottom: 2rem;
  overflow: hidden;
}

.kt-card:hover {
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

.kt-card-header {
  background: #f8f9fa;
  border-bottom: 1px solid #e2e8f0;
  padding: 1.5rem;
  font-weight: 600;
  color: #181c32;
}

.kt-card-body {
  padding: 1.5rem;
}

/* Agent信息卡片 - 与HTML版本完全一致 */
.agent-info-card {
  background: linear-gradient(135deg, #009ef7 0%, #4facfe 100%);
  color: white;
  border: none;
  position: relative;
  overflow: hidden;
}

.agent-info-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 20% 30%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
              radial-gradient(circle at 80% 70%, rgba(255, 255, 255, 0.08) 0%, transparent 50%);
}

.agent-info-card .kt-card-body {
  position: relative;
  z-index: 2;
}

/* 订阅按钮样式 - 与HTML版本完全一致 */
.btn-subscribe-active {
  background: rgba(220, 53, 69, 0.2) !important;
  border-color: #dc3545 !important;
  color: #dc3545 !important;
}

.btn-subscribe-active:hover {
  background: rgba(220, 53, 69, 0.3) !important;
  border-color: #dc3545 !important;
  color: #dc3545 !important;
}

.btn-subscribe-inactive {
  background: transparent !important;
  border-color: rgba(255, 255, 255, 0.4) !important;
  color: white !important;
}

.btn-subscribe-inactive:hover {
  background: rgba(255, 255, 255, 0.15) !important;
  border-color: rgba(255, 255, 255, 0.6) !important;
  color: white !important;
}

/* 订阅按钮禁用状态 */
.btn-subscribe-active:disabled,
.btn-subscribe-inactive:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  pointer-events: none;
}

.btn-subscribe-active:disabled:hover,
.btn-subscribe-inactive:disabled:hover {
  transform: none;
}

/* 统计项样式 */
.stat-item {
  text-align: center;
}

.stat-value {
  font-weight: 700;
  line-height: 1.2;
}

.stat-label {
  color: #6c757d;
  font-size: 0.875rem;
}

/* 内容文本样式 - 与HTML版本完全一致 */
.content-text {
  line-height: 1.8;
  color: #5e6278;
}

.content-text h1, .content-text h2, .content-text h3,
.content-text h4, .content-text h5, .content-text h6 {
  color: #181c32;
  margin-top: 1.5rem;
  margin-bottom: 1rem;
}

.content-text p {
  margin-bottom: 1rem;
}

.content-text ul, .content-text ol {
  margin-bottom: 1rem;
  padding-left: 1.5rem;
}

.content-text li {
  margin-bottom: 0.5rem;
}

/* 轮播图样式 */
.screenshot-carousel {
  border-radius: 0.75rem;
  overflow: hidden;
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
  background: #f8f9fa;
}

.screenshot-container {
  height: 450px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.screenshot-image {
  max-height: 100%;
  max-width: 100%;
  object-fit: contain;
  border-radius: 0.5rem;
  box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
}

.screenshot-image:hover {
  transform: scale(1.02);
}

/* 轮播指示器样式 */
.carousel-indicators {
  bottom: 15px;
}

.carousel-indicators [data-bs-target] {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  margin: 0 4px;
  background-color: rgba(255, 255, 255, 0.5);
  border: 2px solid rgba(255, 255, 255, 0.8);
  transition: all 0.3s ease;
}

.carousel-indicators [data-bs-target].active {
  background-color: #009EF7;
  border-color: #009EF7;
  transform: scale(1.2);
}

/* 控制按钮样式 */
.carousel-control-prev,
.carousel-control-next {
  width: 50px;
  height: 50px;
  top: 50%;
  transform: translateY(-50%);
  background: rgba(0, 0, 0, 0.5);
  border-radius: 50%;
  opacity: 0.7;
  transition: all 0.3s ease;
}

.carousel-control-prev {
  left: 15px;
}

.carousel-control-next {
  right: 15px;
}

.carousel-control-prev:hover,
.carousel-control-next:hover {
  opacity: 1;
  background: rgba(0, 0, 0, 0.7);
  transform: translateY(-50%) scale(1.1);
}

.carousel-control-prev-icon,
.carousel-control-next-icon {
  width: 20px;
  height: 20px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .screenshot-container {
    height: 300px;
  }

  .carousel-control-prev,
  .carousel-control-next {
    width: 40px;
    height: 40px;
  }

  .carousel-control-prev {
    left: 10px;
  }

  .carousel-control-next {
    right: 10px;
  }
}

@media (max-width: 576px) {
  .screenshot-container {
    height: 250px;
  }
}

/* 相关推荐卡片样式 - 与HTML版本完全一致 */
.related-agent-item {
  transition: all 0.3s ease;
  background: #f8f9fa;
  border: 1px solid #e2e8f0 !important;
}

.related-agent-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 158, 247, 0.15);
  border-color: #009ef7 !important;
}

/* 动画效果 */
.animated-number {
  display: inline-block;
  transition: all 0.3s ease;
}

.animated-pulse {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% { opacity: 1; }
  50% { opacity: 0.5; }
  100% { opacity: 1; }
}

/* 确保主要内容区域布局正确 - 与HTML版本完全一致 */
main {
  padding-top: 70px; /* 为固定header留出空间 */
}

.page-header + .container {
  margin-top: 0;
}

/* 确保Bootstrap网格系统正常工作 */
.container-fluid {
  max-width: 1450px;
  margin: 0 auto;
  padding-left: 15px;
  padding-right: 15px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .page-header {
    padding: 2rem 0;
  }

  .agent-icon {
    width: 60px;
    height: 60px;
  }

  .agent-icon i {
    font-size: 2rem;
  }

  .display-4 {
    font-size: 2rem !important;
  }

  .kt-card-header,
  .kt-card-body {
    padding: 1rem;
  }
}
</style>
