<template>
  <div class="api-docs-page">
    <Header />

    <main class="main-content">
      <div class="container mt-4">
        <div class="row">
          <!-- API文档内容 -->
          <div class="col-12">
            <div class="kt-card">
              <div class="kt-card-header">
                <h3 class="mb-0">
                  <i class="fas fa-book me-2 text-primary"></i>
                  智能体矩阵 API 文档
                </h3>
              </div>
              <div class="kt-card-body">
                <div v-if="loading" class="text-center py-5">
                  <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">加载中...</span>
                  </div>
                  <p class="mt-2">正在加载API文档...</p>
                </div>
                <div v-else-if="error" class="alert alert-danger">
                  <i class="fas fa-exclamation-triangle me-2"></i>
                  {{ error }}
                </div>
                <div v-else class="markdown-content" v-html="markdownHtml"></div>
              </div>
            </div>
          </div>

        </div>
      </div>
    </main>

    <Footer />
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { marked } from 'marked'
import Header from '@/components/Header.vue'
import Footer from '@/components/Footer.vue'

// 响应式数据
const loading = ref(true)
const error = ref('')
const markdownHtml = ref('')

// 加载Markdown文档
const loadMarkdownDoc = async () => {
  try {
    loading.value = true
    error.value = ''

    // 从public目录加载Markdown文件
    const response = await fetch('/doc/API_Documentation_Public.md')

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    const markdownText = await response.text()

    // 配置marked选项
    marked.setOptions({
      breaks: true,
      gfm: true,
      headerIds: true,
      mangle: false
    })

    // 将Markdown转换为HTML
    markdownHtml.value = marked(markdownText)

  } catch (err) {
    console.error('加载API文档失败:', err)
    error.value = '加载API文档失败，请稍后重试。'
  } finally {
    loading.value = false
  }
}

// 组件挂载时加载文档
onMounted(() => {
  loadMarkdownDoc()
})
</script>

<style scoped>
.api-docs-page {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}

.main-content {
  flex: 1;
  padding-top: 80px;
  padding-bottom: 2rem;
}

/* Markdown内容样式 */
.markdown-content {
  line-height: 1.6;
  color: #333;
}

/* 标题样式 */
.markdown-content :deep(h1) {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--kt-primary);
  margin-bottom: 1.5rem;
  padding-bottom: 0.5rem;
  border-bottom: 3px solid var(--kt-primary);
}

.markdown-content :deep(h2) {
  font-size: 2rem;
  font-weight: 600;
  color: var(--kt-primary);
  margin-top: 2.5rem;
  margin-bottom: 1.25rem;
  padding-bottom: 0.5rem;
  border-bottom: 2px solid #e9ecef;
}

.markdown-content :deep(h3) {
  font-size: 1.5rem;
  font-weight: 600;
  color: #495057;
  margin-top: 2rem;
  margin-bottom: 1rem;
}

.markdown-content :deep(h4) {
  font-size: 1.25rem;
  font-weight: 600;
  color: #495057;
  margin-top: 1.5rem;
  margin-bottom: 0.75rem;
}

.markdown-content :deep(h5) {
  font-size: 1.1rem;
  font-weight: 600;
  color: #495057;
  margin-top: 1.25rem;
  margin-bottom: 0.5rem;
}

/* 段落样式 */
.markdown-content :deep(p) {
  margin-bottom: 1rem;
  text-align: justify;
}

/* 列表样式 */
.markdown-content :deep(ul),
.markdown-content :deep(ol) {
  margin-bottom: 1rem;
  padding-left: 2rem;
}

.markdown-content :deep(li) {
  margin-bottom: 0.5rem;
}

/* 代码块样式 */
.markdown-content :deep(pre) {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 0.5rem;
  padding: 1.25rem;
  margin: 1.5rem 0;
  border-left: 4px solid var(--kt-primary);
  overflow-x: auto;
  font-size: 0.875rem;
  line-height: 1.5;
}

.markdown-content :deep(code) {
  background: #f8f9fa;
  padding: 0.2rem 0.4rem;
  border-radius: 0.25rem;
  font-size: 0.875rem;
  color: #e83e8c;
  border: 1px solid #e9ecef;
}

.markdown-content :deep(pre code) {
  background: none;
  padding: 0;
  border: none;
  color: inherit;
}

/* 表格样式 */
.markdown-content :deep(table) {
  width: 100%;
  margin-bottom: 1.5rem;
  border-collapse: collapse;
  border: 1px solid #e9ecef;
  border-radius: 0.5rem;
  overflow: hidden;
}

.markdown-content :deep(th),
.markdown-content :deep(td) {
  padding: 0.75rem;
  border-bottom: 1px solid #e9ecef;
  text-align: left;
}

.markdown-content :deep(th) {
  background-color: var(--kt-primary);
  color: white;
  font-weight: 600;
  border-bottom: 2px solid #dee2e6;
}

.markdown-content :deep(tr:nth-child(even)) {
  background-color: #f8f9fa;
}

.markdown-content :deep(tr:hover) {
  background-color: #e9ecef;
}

/* 引用样式 */
.markdown-content :deep(blockquote) {
  border-left: 4px solid var(--kt-primary);
  padding-left: 1rem;
  margin: 1.5rem 0;
  color: #6c757d;
  font-style: italic;
  background: #f8f9fa;
  padding: 1rem;
  border-radius: 0.25rem;
}

/* 分割线样式 */
.markdown-content :deep(hr) {
  border: none;
  height: 2px;
  background: linear-gradient(to right, var(--kt-primary), transparent);
  margin: 2rem 0;
}

/* 链接样式 */
.markdown-content :deep(a) {
  color: var(--kt-primary);
  text-decoration: none;
  border-bottom: 1px solid transparent;
  transition: all 0.3s ease;
}

.markdown-content :deep(a:hover) {
  border-bottom-color: var(--kt-primary);
}

/* 强调样式 */
.markdown-content :deep(strong) {
  font-weight: 600;
  color: #495057;
}

.markdown-content :deep(em) {
  font-style: italic;
  color: #6c757d;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .markdown-content :deep(h1) {
    font-size: 2rem;
  }

  .markdown-content :deep(h2) {
    font-size: 1.5rem;
  }

  .markdown-content :deep(pre) {
    padding: 1rem;
    font-size: 0.8rem;
  }

  .markdown-content :deep(table) {
    font-size: 0.875rem;
  }

  .markdown-content :deep(th),
  .markdown-content :deep(td) {
    padding: 0.5rem;
  }
}
</style>
