<template>
  <div class="api-keys-page">
    <Header />

    <main class="main-content">
      <div class="container mt-4">
        <!-- 页面标题 -->
        <div class="row mb-4">
          <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
              <div>
                <h2><i class="fas fa-key"></i> API密钥管理</h2>
                <p class="text-muted mb-1">管理您的 智能体矩阵 API密钥和访问权限</p>
              </div>
              <div>
                <div v-if="apiKeys.length >= maxKeys" class="mb-2">
                  <small class="text-warning">
                    <i class="fas fa-exclamation-triangle me-1"></i>
                    已达到最大密钥数量限制，请删除不需要的密钥后再创建
                  </small>
                </div>
                <button
                  class="btn btn-primary"
                  @click="showCreateModal = true"
                  :disabled="apiKeys.length >= maxKeys"
                  :title="apiKeys.length >= maxKeys ? '已达到最大密钥数量限制' : '创建新的API密钥'"
                >
                  <i class="fas fa-plus me-1"></i>
                  创建密钥
                  <span v-if="apiKeys.length >= maxKeys" class="badge bg-warning ms-1">已满</span>
                </button>
              </div>
            </div>
          </div>
        </div>

        <!-- 统计信息卡片 -->
        <div class="row mb-4">
          <div class="col-md-3">
            <div class="card stat-card-modern shadow-sm border-0">
              <div class="card-body text-center p-4">
                <h2 class="stat-number text-primary mb-2">{{ apiKeys.length }}</h2>
                <p class="stat-label text-muted mb-0">总密钥数</p>
              </div>
            </div>
          </div>
          <div class="col-md-3">
            <div class="card stat-card-modern shadow-sm border-0">
              <div class="card-body text-center p-4">
                <h2 class="stat-number text-success mb-2">{{ activeKeysCount }}</h2>
                <p class="stat-label text-muted mb-0">活跃密钥</p>
              </div>
            </div>
          </div>
          <div class="col-md-3">
            <div class="card stat-card-modern shadow-sm border-0">
              <div class="card-body text-center p-4">
                <h2 class="stat-number text-warning mb-2">{{ disabledKeysCount }}</h2>
                <p class="stat-label text-muted mb-0">禁用密钥</p>
              </div>
            </div>
          </div>
          <div class="col-md-3">
            <div class="card stat-card-modern shadow-sm border-0">
              <div class="card-body text-center p-4">
                <h2 class="stat-number text-info mb-2">{{ usedKeysCount }}</h2>
                <p class="stat-label text-muted mb-0">已使用</p>
              </div>
            </div>
          </div>
        </div>

        <!-- API密钥列表 -->
        <div class="row">
          <div class="col-12">
            <div class="card">
              <div class="card-header">
                <div class="d-flex justify-content-between align-items-center">
                  <div>
                    <h5 class="mb-1">
                      <i class="fas fa-list"></i> API密钥列表
                      <span class="badge bg-secondary ms-2">{{ apiKeys.length }}</span>
                    </h5>
                    <small class="text-muted">
                      <i class="fas fa-shield-alt me-1"></i>
                      安全提示：请妥善保管您的API密钥，避免泄露给他人
                    </small>
                  </div>
                  <div class="text-end">
                    <small class="text-muted d-block">使用配额</small>
                    <div class="progress" style="width: 100px; height: 6px;">
                      <div
                        class="progress-bar"
                        :class="apiKeys.length >= maxKeys ? 'bg-warning' : 'bg-success'"
                        :style="{ width: (apiKeys.length / maxKeys * 100) + '%' }"
                      ></div>
                    </div>
                    <small class="text-muted">{{ apiKeys.length }}/{{ maxKeys }}</small>
                  </div>
                </div>
              </div>
              <div class="card-body">
                <div v-if="isLoading" class="text-center py-5">
                  <div class="spinner-border" role="status">
                    <span class="visually-hidden">加载中...</span>
                  </div>
                  <p class="mt-2 text-muted">正在加载API密钥...</p>
                </div>

                <div v-else-if="apiKeys.length === 0" class="text-center py-5">
                  <i class="fas fa-key fa-5x text-muted mb-3"></i>
                  <h4 class="text-muted">暂无API密钥</h4>
                  <p class="text-muted">您还没有创建任何API密钥</p>
                  <button
                    class="btn btn-primary"
                    @click="showCreateModal = true"
                  >
                    <i class="fas fa-plus me-1"></i>
                    创建第一个密钥
                  </button>
                </div>

                <div v-else class="table-responsive">
                  <table class="table table-hover align-middle">
                    <thead class="table-dark">
                      <tr>
                        <th width="5%">#</th>
                        <th width="15%">密钥名称</th>
                        <th width="20%">密钥ID</th>
                        <th width="10%">状态</th>
                        <th width="15%">创建时间</th>
                        <th width="15%">最后使用</th>
                        <th width="20%">操作</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr v-for="(apiKey, index) in apiKeys" :key="apiKey.id" class="api-key-row">
                        <!-- ID列 -->
                        <td>
                          <small class="text-muted">#{{ index + 1 }}</small>
                        </td>

                        <!-- 密钥名称 -->
                        <td>
                          <div>
                            <strong>{{ apiKey.keyName }}</strong>
                          </div>
                          <small class="text-muted" v-if="apiKey.description">
                            {{ truncateText(apiKey.description, 30) }}
                          </small>
                        </td>

                        <!-- 密钥ID -->
                        <td>
                          <div class="d-flex align-items-center">
                            <code class="api-key-display me-2">{{ apiKey.keyId }}</code>
                            <button
                              class="btn btn-sm btn-outline-secondary copy-btn"
                              @click="copyToClipboard(apiKey.keyId)"
                              title="复制密钥ID"
                            >
                              <i class="fas fa-copy"></i>
                            </button>
                          </div>
                        </td>

                        <!-- 状态 -->
                        <td>
                          <span
                            v-if="apiKey.status === 1"
                            class="badge bg-success status-badge"
                          >
                            <i class="fas fa-check"></i> 活跃
                          </span>
                          <span
                            v-if="apiKey.status === 0"
                            class="badge bg-warning status-badge"
                          >
                            <i class="fas fa-pause"></i> 禁用
                          </span>
                        </td>

                        <!-- 创建时间 -->
                        <td>
                          <div>
                            <span>{{ formatDate(apiKey.createdTime, 'MM-DD HH:mm') }}</span>
                          </div>
                          <small class="text-muted">{{ formatDate(apiKey.createdTime, 'YYYY') }}</small>
                        </td>

                        <!-- 最后使用时间 -->
                        <td>
                          <div v-if="apiKey.lastUsedAt">
                            <span>{{ formatDate(apiKey.lastUsedAt, 'MM-DD HH:mm') }}</span>
                            <div>
                              <small class="text-muted">{{ formatDate(apiKey.lastUsedAt, 'YYYY') }}</small>
                            </div>
                          </div>
                          <span v-else class="text-muted">从未使用</span>
                        </td>

                        <!-- 操作按钮 -->
                        <td>
                          <div class="action-buttons d-flex gap-1">
                            <button
                              class="btn btn-sm btn-outline-primary"
                              @click="viewKeyDetail(apiKey)"
                              title="查看详情"
                            >
                              <i class="fas fa-eye"></i>
                            </button>
                            <button
                              class="btn btn-sm btn-outline-warning"
                              @click="toggleKeyStatus(apiKey)"
                              :title="apiKey.status === 1 ? '禁用密钥' : '启用密钥'"
                            >
                              <i :class="apiKey.status === 1 ? 'fas fa-pause' : 'fas fa-play'"></i>
                            </button>
                            <button
                              class="btn btn-sm btn-outline-danger"
                              @click="deleteKey(apiKey)"
                              title="删除密钥"
                            >
                              <i class="fas fa-trash"></i>
                            </button>
                          </div>
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          </div>
        </div>


      </div>
    </main>

    <!-- 创建密钥模态框 -->
    <div
      class="modal fade"
      :class="{ show: showCreateModal }"
      :style="{ display: showCreateModal ? 'block' : 'none' }"
      tabindex="-1"
    >
      <div class="modal-dialog modal-lg">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title">
              <i class="fas fa-plus-circle"></i> 创建API密钥
            </h5>
            <button
              type="button"
              class="btn-close"
              @click="showCreateModal = false"
            ></button>
          </div>
          <form @submit.prevent="createApiKey">
            <div class="modal-body">
              <div class="row g-3">
                <div class="col-12">
                  <div class="form-floating">
                    <input
                      type="text"
                      class="form-control"
                      id="keyName"
                      v-model="createForm.keyName"
                      placeholder="密钥名称"
                      required
                      maxlength="50"
                    >
                    <label for="keyName">密钥名称 *</label>
                  </div>
                </div>
                <div class="col-12">
                  <div class="form-floating">
                    <textarea
                      class="form-control"
                      id="description"
                      v-model="createForm.description"
                      placeholder="密钥描述"
                      style="height: 80px"
                      maxlength="200"
                    ></textarea>
                    <label for="description">密钥描述</label>
                  </div>
                </div>
                <div class="col-md-4">
                  <div class="form-floating">
                    <input
                      type="number"
                      class="form-control limit-input"
                      id="rateLimit"
                      v-model="createForm.rateLimit"
                      placeholder="速率限制"
                      value="1000"
                      min="1"
                      max="10000"
                    >
                    <label for="rateLimit">速率限制 (次/分钟)</label>
                  </div>
                </div>
                <div class="col-md-4">
                  <div class="form-floating">
                    <input
                      type="number"
                      class="form-control limit-input"
                      id="dailyLimit"
                      v-model="createForm.dailyLimit"
                      placeholder="日限制"
                      value="10000"
                      min="1"
                      max="100000"
                    >
                    <label for="dailyLimit">日限制 (次/天)</label>
                  </div>
                </div>
                <div class="col-md-4">
                  <div class="form-floating">
                    <input
                      type="number"
                      class="form-control limit-input"
                      id="monthlyLimit"
                      v-model="createForm.monthlyLimit"
                      placeholder="月限制"
                      value="100000"
                      min="1"
                      max="1000000"
                    >
                    <label for="monthlyLimit">月限制 (次/月)</label>
                  </div>
                </div>
                <div class="col-12">
                  <div class="form-floating">
                    <textarea
                      class="form-control"
                      id="allowedIps"
                      v-model="createForm.allowedIps"
                      placeholder="允许的IP地址"
                      style="height: 60px"
                    ></textarea>
                    <label for="allowedIps">允许的IP地址 (可选，多个IP用逗号分隔)</label>
                  </div>
                </div>
                <div class="col-12">
                  <div class="form-floating">
                    <textarea
                      class="form-control"
                      id="allowedDomains"
                      v-model="createForm.allowedDomains"
                      placeholder="允许的域名"
                      style="height: 60px"
                    ></textarea>
                    <label for="allowedDomains">允许的域名 (可选，多个域名用逗号分隔)</label>
                  </div>
                </div>
              </div>
            </div>
            <div class="modal-footer">
              <button type="button" class="btn btn-secondary" @click="showCreateModal = false">取消</button>
              <button
                type="submit"
                class="btn btn-primary"
                :disabled="isCreating"
              >
                <i v-if="isCreating" class="fas fa-spinner fa-spin"></i>
                <i v-else class="fas fa-plus"></i>
                {{ isCreating ? '创建中...' : '创建密钥' }}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>

    <!-- 模态框背景 -->
    <div
      v-if="showCreateModal || showDetailModal"
      class="modal-backdrop fade show"
      @click="closeModals"
    ></div>

    <!-- 密钥详情模态框 -->
    <div
      class="modal fade"
      :class="{ show: showDetailModal }"
      :style="{ display: showDetailModal ? 'block' : 'none' }"
      tabindex="-1"
    >
      <div class="modal-dialog modal-xl">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title">
              <i class="fas fa-info-circle"></i> API密钥详情
            </h5>
            <button
              type="button"
              class="btn-close"
              @click="showDetailModal = false"
            ></button>
          </div>
          <div class="modal-body" style="max-height: 80vh; overflow-y: auto;">
            <div v-if="isLoadingDetail" class="text-center py-4">
              <div class="spinner-border" role="status">
                <span class="visually-hidden">加载中...</span>
              </div>
              <p class="mt-2 text-muted">正在加载详情...</p>
            </div>
            <div v-else-if="selectedKey" class="card">
              <div class="card-header">
                <h6 class="mb-0"><i class="fas fa-info-circle"></i> 基本信息</h6>
              </div>
              <div class="card-body">
                <div class="alert alert-warning mb-3">
                  <i class="fas fa-exclamation-triangle"></i>
                  <strong>安全提醒:</strong> 请妥善保管您的密钥，不要在公共场所或不安全的环境中暴露密钥信息。
                </div>
                <div class="row">
                  <div class="col-md-6">
                    <table class="table table-sm table-borderless">
                      <tr><td class="fw-bold">密钥名称:</td><td>{{ selectedKey.keyName }}</td></tr>
                      <tr>
                        <td class="fw-bold">密钥ID:</td>
                        <td>
                          <code>{{ selectedKey.keyId }}</code>
                          <button
                            class="btn btn-sm btn-outline-secondary ms-2"
                            @click="copyToClipboard(selectedKey.keyId)"
                            title="复制密钥ID"
                          >
                            <i class="fas fa-copy"></i>
                          </button>
                        </td>
                      </tr>
                      <tr>
                        <td class="fw-bold">状态:</td>
                        <td>
                          <span :class="selectedKey.status === 1 ? 'badge bg-success' : 'badge bg-warning'">
                            {{ selectedKey.status === 1 ? '活跃' : '禁用' }}
                          </span>
                        </td>
                      </tr>
                      <tr><td class="fw-bold">创建时间:</td><td>{{ formatDateTime(selectedKey.createdTime) }}</td></tr>
                    </table>
                  </div>
                  <div class="col-md-6">
                    <table class="table table-sm table-borderless">
                      <tr><td class="fw-bold">最后使用:</td><td>{{ selectedKey.lastUsedAt ? formatDateTime(selectedKey.lastUsedAt) : '从未使用' }}</td></tr>
                      <tr><td class="fw-bold">最后使用IP:</td><td>{{ selectedKey.lastUsedIp || 'N/A' }}</td></tr>
                      <tr><td class="fw-bold">过期时间:</td><td>{{ selectedKey.expiresAt ? formatDateTime(selectedKey.expiresAt) : '永不过期' }}</td></tr>
                    </table>
                  </div>
                </div>
                <div v-if="selectedKey.description" class="mt-3">
                  <strong>描述:</strong> {{ selectedKey.description }}
                </div>
              </div>
            </div>
          </div>
          <div class="modal-footer">
            <button type="button" class="btn btn-primary" @click="showDetailModal = false">关闭</button>
          </div>
        </div>
      </div>
    </div>

    <Footer />
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { useToast } from 'vue-toastification'
import { formatDate, fromNow } from '@/utils/helpers'
import api from '@/utils/api'
import Header from '@/components/Header.vue'
import Footer from '@/components/Footer.vue'

const router = useRouter()
const authStore = useAuthStore()
const toast = useToast()

// 响应式数据
const apiKeys = ref([])
const isLoading = ref(false)
const isCreating = ref(false)
const isLoadingDetail = ref(false)

const showCreateModal = ref(false)
const showDetailModal = ref(false)
const selectedKey = ref(null)
const maxKeys = ref(5)

// 计算属性
const activeKeysCount = computed(() =>
  apiKeys.value.filter(key => key.status === 1).length
)

const disabledKeysCount = computed(() =>
  apiKeys.value.filter(key => key.status === 0).length
)

const usedKeysCount = computed(() =>
  apiKeys.value.filter(key => key.lastUsedAt != null).length
)

const createForm = reactive({
  keyName: '',
  description: '',
  rateLimit: 1000,
  dailyLimit: 10000,
  monthlyLimit: 100000,
  allowedIps: '',
  allowedDomains: ''
})

// 方法
const fetchApiKeys = async () => {
  isLoading.value = true
  try {
    const response = await api.get('/api/v1/dashboard/api-keys')
    if (response.data.success) {
      apiKeys.value = response.data.data || []
      console.log('API密钥数据加载成功:', apiKeys.value)
    } else {
      console.error('获取API密钥失败:', response.data.message)
      toast.error(response.data.message || '获取API密钥失败')
      apiKeys.value = []
    }
  } catch (error) {
    console.error('获取API密钥失败:', error)
    if (error.response?.status === 401) {
      toast.error('登录已过期，请重新登录')
      authStore.logout()
      router.push('/auth/login')
    } else {
      toast.error('获取API密钥失败')
    }
    apiKeys.value = []
  } finally {
    isLoading.value = false
  }
}

const createApiKey = async () => {
  if (!createForm.keyName.trim()) {
    toast.error('请输入密钥名称')
    return
  }

  isCreating.value = true
  try {
    const requestData = {
      keyName: createForm.keyName.trim(),
      description: createForm.description || ''
    }

    const response = await api.post('/api/v1/dashboard/api-keys', requestData, {
      headers: {
        'Content-Type': 'application/json'
      }
    })

    if (response.data.success) {
      showCreateModal.value = false

      // 重置表单
      Object.assign(createForm, {
        keyName: '',
        description: '',
        rateLimit: 1000,
        dailyLimit: 10000,
        monthlyLimit: 100000,
        allowedIps: '',
        allowedDomains: ''
      })

      // 重新加载密钥列表
      await fetchApiKeys()

      // 调试：打印返回的数据结构
      console.log('API密钥创建响应数据:', response.data)
      console.log('data字段:', response.data.data)
      if (response.data.data) {
        console.log('keySecret字段:', response.data.data.keySecret)
      }

      // 显示API密钥信息弹框
      if (response.data.data && response.data.data.keySecret) {
        console.log('显示SweetAlert弹框')
        await showApiKeySecret(response.data.data)
      } else {
        console.log('没有keySecret，显示普通提示')
        toast.success('API密钥创建成功！')
      }
    } else {
      toast.error(response.data.message || '创建失败')
    }
  } catch (error) {
    console.error('创建API密钥失败:', error)
    // 尝试从错误响应中获取具体的错误信息
    let errorMessage = '创建API密钥失败'
    if (error.response?.data?.message) {
      errorMessage = error.response.data.message
    } else if (error.message) {
      errorMessage = error.message
    }
    toast.error(errorMessage)
  } finally {
    isCreating.value = false
  }
}

const copyToClipboard = async (text) => {
  try {
    await navigator.clipboard.writeText(text)
    toast.success('已复制到剪贴板')
  } catch (error) {
    // 降级方案
    const textArea = document.createElement('textarea')
    textArea.value = text
    document.body.appendChild(textArea)
    textArea.select()
    document.execCommand('copy')
    document.body.removeChild(textArea)
    toast.success('已复制到剪贴板')
  }
}

const viewKeyDetail = async (apiKey) => {
  selectedKey.value = null
  showDetailModal.value = true
  isLoadingDetail.value = true

  try {
    const response = await api.get(`/api/v1/dashboard/api-keys/${apiKey.id}`)
    if (response.data.success) {
      selectedKey.value = response.data.data
    } else {
      toast.error(response.data.message || '获取详情失败')
    }
  } catch (error) {
    console.error('获取密钥详情失败:', error)
    toast.error('获取详情失败')
  } finally {
    isLoadingDetail.value = false
  }
}

const toggleKeyStatus = async (apiKey) => {
  const action = apiKey.status === 1 ? '禁用' : '启用'

  // 使用SweetAlert2确认对话框
  const result = await window.Swal.fire({
    title: `${action}API密钥`,
    text: `确定要${action}这个API密钥吗？`,
    icon: 'question',
    showCancelButton: true,
    confirmButtonColor: apiKey.status === 1 ? '#dc3545' : '#28a745',
    cancelButtonColor: '#6c757d',
    confirmButtonText: `确定${action}`,
    cancelButtonText: '取消',
    reverseButtons: true
  })

  if (!result.isConfirmed) {
    return
  }

  try {
    const response = await api.post(`/api/v1/dashboard/api-keys/${apiKey.id}/toggle`)

    if (response.data.success) {
      apiKey.status = apiKey.status === 1 ? 0 : 1
      toast.success(`密钥已${action}`)
    } else {
      toast.error(response.data.message || `${action}失败`)
    }
  } catch (error) {
    console.error('切换密钥状态失败:', error)
    toast.error('操作失败')
  }
}

const deleteKey = async (apiKey) => {
  // 使用SweetAlert2确认对话框
  const result = await window.Swal.fire({
    title: '删除API密钥',
    html: `确定要删除密钥 <strong>"${apiKey.keyName}"</strong> 吗？<br><br><span class="text-danger">此操作不可撤销！</span>`,
    icon: 'warning',
    showCancelButton: true,
    confirmButtonColor: '#dc3545',
    cancelButtonColor: '#6c757d',
    confirmButtonText: '确定删除',
    cancelButtonText: '取消',
    reverseButtons: true,
    focusCancel: true
  })

  if (!result.isConfirmed) {
    return
  }

  try {
    // 使用POST方法，在URL中添加/delete后缀
    const response = await api.post(`/api/v1/dashboard/api-keys/${apiKey.id}/delete`)

    if (response.data.success) {
      apiKeys.value = apiKeys.value.filter(key => key.id !== apiKey.id)
      toast.success('密钥已删除')
    } else {
      toast.error(response.data.message || '删除失败')
    }
  } catch (error) {
    console.error('删除密钥失败:', error)
    toast.error('删除失败')
  }
}

// 显示API密钥Secret的弹框
const showApiKeySecret = async (apiKeyData) => {
  console.log('showApiKeySecret被调用，参数:', apiKeyData)

  const keySecret = apiKeyData.keySecret
  const keyId = apiKeyData.keyId
  const keyName = apiKeyData.keyName || '新API密钥'

  console.log('提取的数据:', { keySecret, keyId, keyName })

  // 创建复制按钮的HTML
  const copyButtonHtml = `
    <div class="mt-3">
      <div class="input-group">
        <input type="text" class="form-control" id="keySecretInput" value="${keySecret}" readonly style="font-family: monospace; font-size: 14px;">
        <button class="btn btn-outline-primary" type="button" id="copySecretBtn">
          <i class="fas fa-copy"></i> 复制
        </button>
      </div>
    </div>
  `

  const result = await window.Swal.fire({
    title: '🎉 API密钥创建成功！',
    html: `
      <div class="text-start">
        <p class="mb-2"><strong>密钥名称：</strong>${keyName}</p>
        <p class="mb-2"><strong>密钥ID：</strong><code>${keyId}</code></p>
        <p class="mb-3"><strong>密钥Secret：</strong></p>
        ${copyButtonHtml}
        <div class="alert alert-warning mt-3 mb-0">
          <i class="fas fa-exclamation-triangle me-2"></i>
          <strong>重要提示：</strong>这是唯一一次显示完整密钥的机会！<br>
          请立即复制并妥善保存，关闭后将无法再次查看。
        </div>
      </div>
    `,
    icon: 'success',
    width: '600px',
    showCancelButton: false,
    confirmButtonText: '我已保存，关闭',
    confirmButtonColor: '#009EF7',
    allowOutsideClick: false,
    allowEscapeKey: false,
    didOpen: () => {
      // 添加复制功能
      const copyBtn = document.getElementById('copySecretBtn')
      const input = document.getElementById('keySecretInput')

      copyBtn.addEventListener('click', async () => {
        try {
          await navigator.clipboard.writeText(keySecret)
          copyBtn.innerHTML = '<i class="fas fa-check"></i> 已复制'
          copyBtn.classList.remove('btn-outline-primary')
          copyBtn.classList.add('btn-success')

          // 2秒后恢复原状
          setTimeout(() => {
            copyBtn.innerHTML = '<i class="fas fa-copy"></i> 复制'
            copyBtn.classList.remove('btn-success')
            copyBtn.classList.add('btn-outline-primary')
          }, 2000)
        } catch (err) {
          // 降级到选择文本的方式
          input.select()
          input.setSelectionRange(0, 99999)
          document.execCommand('copy')

          copyBtn.innerHTML = '<i class="fas fa-check"></i> 已复制'
          copyBtn.classList.remove('btn-outline-primary')
          copyBtn.classList.add('btn-success')

          setTimeout(() => {
            copyBtn.innerHTML = '<i class="fas fa-copy"></i> 复制'
            copyBtn.classList.remove('btn-success')
            copyBtn.classList.add('btn-outline-primary')
          }, 2000)
        }
      })

      // 自动选中密钥文本
      input.select()
    }
  })

  // 用户确认后显示成功提示
  if (result.isConfirmed) {
    toast.success('API密钥创建完成！请确保已保存密钥信息。')
  }
}

// 工具方法
const truncateText = (text, length) => {
  if (!text) return ''
  return text.length > length ? text.substring(0, length) + '...' : text
}

const formatDateTime = (dateTimeString) => {
  if (!dateTimeString) return 'N/A'
  const date = new Date(dateTimeString)
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

const closeModals = () => {
  showCreateModal.value = false
  showDetailModal.value = false
}

// 生命周期
onMounted(() => {
  fetchApiKeys()
})
</script>

<style scoped>
.api-keys-page {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}

.main-content {
  flex: 1;
  padding-bottom: 2rem;
}

/* 统计卡片样式 */
.stat-card-modern {
  transition: all 0.3s ease;
  border-radius: 12px;
}

.stat-card-modern:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1) !important;
}

.stat-number {
  font-size: 2.5rem;
  font-weight: 700;
  line-height: 1;
}

.stat-label {
  font-size: 0.875rem;
  font-weight: 500;
}

/* 表格样式 */
.table-hover tbody tr:hover {
  background-color: rgba(0, 123, 255, 0.05);
}

.api-key-row {
  transition: all 0.2s ease;
}

.api-key-display {
  font-family: 'Courier New', monospace;
  font-size: 0.8rem;
  background: #f8f9fa;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  border: 1px solid #dee2e6;
}

.copy-btn {
  padding: 0.25rem 0.5rem;
  font-size: 0.75rem;
}

.status-badge {
  font-size: 0.75rem;
  padding: 0.35rem 0.65rem;
}

.action-buttons .btn {
  padding: 0.25rem 0.5rem;
  font-size: 0.75rem;
}

/* 模态框样式 */
.modal {
  background: rgba(0, 0, 0, 0.5);
}

.modal-content {
  border: none;
  border-radius: 12px;
  box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.175);
}

.modal-header {
  border-bottom: 1px solid #e9ecef;
  color: white;
  border-radius: 12px 12px 0 0;
}

.modal-header .btn-close {
  filter: invert(1);
}

.modal-footer {
  border-top: 1px solid #e9ecef;
  background: #f8f9fa;
  border-radius: 0 0 12px 12px;
}

/* 表单样式 */
.form-floating > .form-control {
  border-radius: 8px;
}

.limit-input {
  text-align: center;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .stat-number {
    font-size: 2rem;
  }

  .table-responsive {
    font-size: 0.875rem;
  }

  .action-buttons {
    flex-direction: column;
  }

  .action-buttons .btn {
    margin-bottom: 0.25rem;
    width: 100%;
  }

  .d-flex.justify-content-between {
    flex-direction: column;
    align-items: flex-start !important;
  }

  .d-flex.justify-content-between > div:last-child {
    margin-top: 1rem;
    width: 100%;
  }
}

@media (max-width: 576px) {
  .modal-dialog {
    margin: 0.5rem;
  }

  .modal-xl {
    max-width: none;
  }
}
</style>
