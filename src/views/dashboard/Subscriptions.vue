<template>
  <div class="subscriptions-page">
    <Header />
    
    <main class="main-content" style="    padding-top: 0.75rem;">
      <div class="container mt-4">
        <!-- 页面标题 -->
        <div class="row mb-4">
          <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
              <div>
                <h2><i class="fas fa-star"></i> 我的订阅</h2>
                <p class="text-muted">管理您的 智能体矩阵 订阅服务</p>
              </div>
              <div>
                <div class="btn-group" role="group" aria-label="视图切换">
                  <button 
                    type="button" 
                    class="btn btn-outline-primary view-toggle-btn"
                    :class="{ active: currentView === 'table' }"
                    @click="switchView('table')"
                  >
                    <i class="fas fa-table me-1"></i> 表格
                  </button>
                  <button 
                    type="button" 
                    class="btn btn-outline-primary view-toggle-btn"
                    :class="{ active: currentView === 'card' }"
                    @click="switchView('card')"
                  >
                    <i class="fas fa-th-large me-1"></i> 卡片
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 统计信息卡片 -->
        <div class="row mb-4">
          <div class="col-md-3">
            <div class="card stat-card-modern shadow-sm border-0">
              <div class="card-body text-center p-4">
                <h2 class="stat-number text-primary mb-2">
                  {{ animatedStats.total }}
                </h2>
                <p class="stat-label text-muted mb-0">总订阅数</p>
              </div>
            </div>
          </div>
          <div class="col-md-3">
            <div class="card stat-card-modern shadow-sm border-0">
              <div class="card-body text-center p-4">
                <h2 class="stat-number text-success mb-2">
                  {{ animatedStats.active }}
                </h2>
                <p class="stat-label text-muted mb-0">活跃订阅</p>
              </div>
            </div>
          </div>
          <div class="col-md-3">
            <div class="card stat-card-modern shadow-sm border-0">
              <div class="card-body text-center p-4">
                <h2 class="stat-number text-danger mb-2">
                  {{ animatedStats.cancelled }}
                </h2>
                <p class="stat-label text-muted mb-0">已取消订阅</p>
              </div>
            </div>
          </div>
          <div class="col-md-3">
            <div class="card stat-card-modern shadow-sm border-0">
              <div class="card-body text-center p-4">
                <h2 class="stat-number text-info mb-2">
                  {{ animatedStats.activeRate }}%
                </h2>
                <p class="stat-label text-muted mb-0">活跃率</p>
              </div>
            </div>
          </div>
        </div>

        <!-- 订阅内容区域 -->
        <div class="row">
          <div class="col-12">
            <div class="card">
              <div class="card-header">
                <h5 class="mb-0">
                  <i class="fas fa-star"></i> 订阅记录
                  <span class="badge bg-secondary ms-2">{{ subscriptions.length }}</span>
                </h5>
              </div>
              <div class="card-body">
                <!-- 表格视图 -->
                <div v-show="currentView === 'table'" id="tableViewContainer">
                  <div v-if="subscriptions.length > 0" class="table-responsive">
                    <table class="table table-hover align-middle">
                      <thead class="table-dark">
                        <tr>
                          <th width="5%">#</th>
                          <th width="25%">Agent信息</th>
                          <th width="15%">分类</th>
                          <th width="10%">状态</th>
                          <th width="15%">订阅时间</th>
                          <th width="10%">订阅天数</th>
                          <th width="20%">操作</th>
                        </tr>
                      </thead>
                      <tbody>
                        <tr 
                          v-for="subscription in subscriptions" 
                          :key="subscription.subscriptionId"
                          class="subscription-row"
                        >
                          <!-- ID列 -->
                          <td>
                            <small class="text-muted">#{{ subscription.subscriptionId }}</small>
                          </td>

                          <!-- Agent信息 -->
                          <td>
                            <div>
                              <strong>{{ subscription.agentName || subscription.displayName }}</strong>
                            </div>
                            <small v-if="subscription.agentTypeText" class="text-muted">
                              <i class="fas fa-tag"></i>
                              {{ subscription.agentTypeText }}
                            </small>
                          </td>

                          <!-- 分类信息 -->
                          <td>
                            <div>{{ subscription.categoryDisplayName || subscription.categoryName }}</div>
                            <small class="text-muted">{{ subscription.businessTypeDisplayName || subscription.businessTypeName }}</small>
                          </td>

                          <!-- 订阅状态 -->
                          <td>
                            <span :class="`badge ${getSubscriptionStatusClass(subscription.subscriptionStatus)}`">
                              <i :class="subscription.subscriptionStatus === 1 ? 'fas fa-check' : 'fas fa-times'"></i>
                              {{ getSubscriptionStatusText(subscription.subscriptionStatus) }}
                            </span>
                          </td>

                          <!-- 订阅时间 -->
                          <td>
                            <div>{{ formatDate(subscription.subscriptionTime, 'MM-DD HH:mm') }}</div>
                            <small class="text-muted">{{ formatDate(subscription.subscriptionTime, 'YYYY') }}</small>
                          </td>

                          <!-- 订阅天数 -->
                          <td>
                            <span class="badge bg-info">{{ getSubscriptionDays(subscription.subscriptionTime) }} 天</span>
                          </td>

                          <!-- 操作按钮 -->
                          <td>
                            <div class="action-buttons d-flex gap-1">
                              <router-link 
                                :to="`/agent/detail/${subscription.agentId}`"
                                class="btn btn-sm btn-outline-primary"
                                title="查看详情"
                              >
                                <i class="fas fa-eye"></i>
                              </router-link>
                              <button 
                                v-if="subscription.subscriptionStatus === 1"
                                class="btn btn-sm btn-outline-danger"
                                @click="showUnsubscribeModal(subscription)"
                                title="取消订阅"
                              >
                                <i class="fas fa-heart-broken"></i>
                              </button>
                            </div>
                          </td>
                        </tr>
                      </tbody>
                    </table>
                  </div>

                  <!-- 表格视图空状态 -->
                  <div v-else class="text-center py-5">
                    <i class="fas fa-star fa-5x text-muted mb-3"></i>
                    <h4 class="text-muted">暂无订阅记录</h4>
                    <p class="text-muted">您还没有订阅任何智能体矩阵服务</p>
                  </div>
                </div>

                <!-- 卡片视图 -->
                <div v-show="currentView === 'card'" id="cardViewContainer">
                  <div v-if="subscriptions.length > 0" class="row">
                    <div 
                      v-for="subscription in subscriptions" 
                      :key="subscription.subscriptionId"
                      class="col-md-6 col-lg-4 mb-3"
                    >
                      <div class="card subscription-card h-100">
                        <div class="card-header d-flex justify-content-between align-items-center">
                          <small class="text-muted">#{{ subscription.subscriptionId }}</small>
                          <span :class="`badge status-badge ${getSubscriptionStatusClass(subscription.subscriptionStatus)}`">
                            <i :class="subscription.subscriptionStatus === 1 ? 'fas fa-check' : 'fas fa-times'"></i>
                            {{ getSubscriptionStatusText(subscription.subscriptionStatus) }}
                          </span>
                        </div>

                        <div class="card-body">
                          <h6 class="card-title">
                            <i class="fas fa-robot text-primary"></i>
                            {{ subscription.agentName || subscription.displayName }}
                          </h6>
                          <div class="mb-2">
                            <span class="badge bg-primary me-1">{{ subscription.categoryDisplayName || subscription.categoryName }}</span>
                            <span class="badge bg-info me-1">{{ subscription.businessTypeDisplayName || subscription.businessTypeName }}</span>
                            <span v-if="subscription.agentTypeText" class="badge bg-secondary">{{ subscription.agentTypeText }}</span>
                          </div>
                          <div class="small text-muted mb-2">
                            <div>
                              <i class="fas fa-clock"></i>
                              {{ formatDate(subscription.subscriptionTime, 'YYYY-MM-DD HH:mm') }}
                            </div>
                            <div>
                              <i class="fas fa-calendar-alt"></i>
                              {{ getSubscriptionDays(subscription.subscriptionTime) }} 天
                            </div>
                          </div>
                        </div>

                        <div class="card-footer">
                          <div class="d-flex justify-content-between align-items-center">
                            <router-link 
                              :to="`/agent/detail/${subscription.agentId}`"
                              class="btn btn-sm btn-outline-primary"
                            >
                              <i class="fas fa-eye me-1"></i>查看详情
                            </router-link>
                            <button 
                              v-if="subscription.subscriptionStatus === 1"
                              class="btn btn-sm btn-outline-danger"
                              @click="showUnsubscribeModal(subscription)"
                            >
                              <i class="fas fa-heart-broken me-1"></i>取消订阅
                            </button>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- 卡片视图空状态 -->
                  <div v-else class="text-center py-5">
                    <i class="fas fa-star fa-5x text-muted mb-3"></i>
                    <h4 class="text-muted">暂无订阅记录</h4>
                    <p class="text-muted">您还没有订阅任何智能体矩阵服务</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </main>

    <!-- 取消订阅确认模态框 -->
    <div 
      class="modal fade" 
      id="unsubscribeModal" 
      tabindex="-1" 
      aria-labelledby="unsubscribeModalLabel" 
      aria-hidden="true"
      ref="unsubscribeModal"
    >
      <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
          <div class="modal-header border-0">
            <h5 class="modal-title fw-bold" id="unsubscribeModalLabel">
              <i class="fas fa-exclamation-triangle text-warning me-2"></i>确认取消订阅
            </h5>
            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
          </div>
          <div class="modal-body">
            <p class="mb-3">
              您确定要取消订阅 <strong>{{ selectedSubscription?.displayName }}</strong> 吗？
            </p>
            <div class="alert alert-warning border-0">
              <i class="fas fa-info-circle me-2"></i>
              <small>取消订阅后，您将无法继续使用该 Agent 的服务。</small>
            </div>
          </div>
          <div class="modal-footer border-0">
            <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">
              <i class="fas fa-times me-1"></i> 取消
            </button>
            <button 
              type="button" 
              class="btn btn-danger" 
              @click="confirmUnsubscribe"
              :disabled="isUnsubscribing"
            >
              <span v-if="isUnsubscribing">
                <i class="fas fa-spinner fa-spin me-1"></i> 处理中...
              </span>
              <span v-else>
                <i class="fas fa-heart-broken me-1"></i> 确认取消订阅
              </span>
            </button>
          </div>
        </div>
      </div>
    </div>

    <Footer />
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { useToast } from 'vue-toastification'
import Header from '@/components/Header.vue'
import Footer from '@/components/Footer.vue'
import dayjs from 'dayjs'
import api from '@/utils/api'

const router = useRouter()
const authStore = useAuthStore()
const toast = useToast()

// 响应式数据
const currentView = ref('table')
const subscriptions = ref([])
const selectedSubscription = ref(null)
const isUnsubscribing = ref(false)
const unsubscribeModal = ref(null)

// 统计数据
const animatedStats = reactive({
  total: 0,
  active: 0,
  cancelled: 0,
  activeRate: 0
})

// 计算属性
const stats = computed(() => {
  const total = subscriptions.value.length
  const active = subscriptions.value.filter(s => s.subscriptionStatus === 1).length
  const cancelled = total - active
  const activeRate = total > 0 ? Math.round((active / total) * 100) : 0

  return { total, active, cancelled, activeRate }
})

// 方法
const switchView = (view) => {
  currentView.value = view
  localStorage.setItem('subscriptionView', view)
}

const formatDate = (dateString, format = 'YYYY-MM-DD HH:mm') => {
  if (!dateString) return '未知'
  return dayjs(dateString).format(format)
}

// 计算订阅天数
const getSubscriptionDays = (subscriptionTime) => {
  if (!subscriptionTime) return 0
  return dayjs().diff(dayjs(subscriptionTime), 'day')
}

// 获取订阅状态文本
const getSubscriptionStatusText = (status) => {
  switch (status) {
    case 1:
      return '已订阅'
    case 0:
      return '已取消'
    default:
      return '未知'
  }
}

// 获取订阅状态CSS类
const getSubscriptionStatusClass = (status) => {
  switch (status) {
    case 1:
      return 'bg-success'
    case 0:
      return 'bg-danger'
    default:
      return 'bg-secondary'
  }
}

const showUnsubscribeModal = (subscription) => {
  selectedSubscription.value = subscription

  // 使用Bootstrap模态框
  nextTick(() => {
    if (unsubscribeModal.value && typeof bootstrap !== 'undefined') {
      const modal = new bootstrap.Modal(unsubscribeModal.value)
      modal.show()
    }
  })
}

const confirmUnsubscribe = async () => {
  if (!selectedSubscription.value) return

  isUnsubscribing.value = true

  try {
    // 使用Vue Agent Controller的订阅接口
    const response = await api.post(`/api/v1/agent/${selectedSubscription.value.agentId}/toggle-subscription`)
    const data = response.data

    if (data.success && !data.isSubscribed) {
      toast.success(`已成功取消订阅 ${selectedSubscription.value.agentName || selectedSubscription.value.displayName}`)

      // 关闭模态框
      if (unsubscribeModal.value && typeof bootstrap !== 'undefined') {
        const modal = bootstrap.Modal.getInstance(unsubscribeModal.value)
        if (modal) modal.hide()
      }

      // 刷新数据
      setTimeout(() => {
        loadSubscriptions()
      }, 1000)
    } else {
      toast.error(data.message || '取消订阅失败，请稍后重试')
    }
  } catch (error) {
    console.error('取消订阅失败:', error)
    if (error.response?.status === 401) {
      toast.error('登录已过期，请重新登录')
      authStore.logout()
      router.push('/auth/login')
    } else {
      toast.error('网络错误，请稍后重试')
    }
  } finally {
    isUnsubscribing.value = false
  }
}

const loadSubscriptions = async () => {
  try {
    // 检查用户是否已登录
    if (!authStore.isAuthenticated) {
      toast.error('请先登录')
      router.push('/auth/login')
      return
    }

    const response = await api.get('/api/v1/dashboard/subscriptions')
    const data = response.data

    if (data.success) {
      subscriptions.value = data.data || []
      console.log('订阅数据加载成功:', subscriptions.value)
      animateStats()
    } else {
      console.error('加载订阅数据失败:', data.message)
      toast.error(data.message || '加载订阅数据失败')
      subscriptions.value = []
    }
  } catch (error) {
    console.error('加载订阅数据失败:', error)
    if (error.response?.status === 401) {
      toast.error('登录已过期，请重新登录')
      authStore.logout()
      router.push('/auth/login')
    } else {
      toast.error('网络错误，请稍后重试')
      subscriptions.value = []
    }
    animateStats()
  }
}

// 跳转到Agent详情页面
const goToAgentDetail = (agentId) => {
  router.push(`/agent/detail/${agentId}`)
}

const animateStats = () => {
  const targetStats = stats.value

  // 重置动画数据
  animatedStats.total = 0
  animatedStats.active = 0
  animatedStats.cancelled = 0
  animatedStats.activeRate = 0

  // 动画函数
  const animateNumber = (key, target, duration = 1500) => {
    const increment = target / (duration / 16)
    let current = 0

    const timer = setInterval(() => {
      current += increment
      if (current >= target) {
        current = target
        clearInterval(timer)
      }
      animatedStats[key] = Math.floor(current)
    }, 16)
  }

  // 延迟启动动画，创建波浪效果
  setTimeout(() => animateNumber('total', targetStats.total), 0)
  setTimeout(() => animateNumber('active', targetStats.active), 200)
  setTimeout(() => animateNumber('cancelled', targetStats.cancelled), 400)
  setTimeout(() => animateNumber('activeRate', targetStats.activeRate), 600)
}

// 生命周期
onMounted(() => {
  // 从localStorage恢复视图设置
  const savedView = localStorage.getItem('subscriptionView') || 'table'
  currentView.value = savedView

  // 加载订阅数据
  loadSubscriptions()
})
</script>

<style scoped>
/* 订阅页面样式 - 与HTML版本保持一致 */
.subscriptions-page {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.main-content {
  flex: 1;
  min-height: calc(100vh - 140px); /* 减去Header和Footer的大概高度 */
}

.subscription-row {
  transition: all 0.2s ease;
}

.subscription-row:hover {
  background-color: #f8f9fa !important;
  transform: translateX(2px);
}

.table th {
  border-top: none;
  font-weight: 600;
  font-size: 0.9rem;
}

.table td {
  vertical-align: middle;
  font-size: 0.9rem;
}

.badge {
  font-size: 0.75rem;
}

.action-buttons .btn {
  min-width: 32px;
  height: 32px;
  padding: 4px 8px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.table-responsive {
  border-radius: 0.5rem;
  overflow: hidden;
}

/* 卡片视图样式 */
.subscription-card {
  transition: all 0.3s ease;
  cursor: pointer;
}

.subscription-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.status-badge {
  font-size: 0.75rem;
}

/* 视图切换按钮样式 */
.view-toggle-btn {
  background-color: #FFFFFF !important;
  border-color: #E4E6EA !important;
  color: #5E6278 !important;
  transition: all 0.3s ease !important;
}

.view-toggle-btn:hover {
  background-color: #F9F9F9 !important;
  border-color: #009EF7 !important;
  color: #009EF7 !important;
}

.view-toggle-btn.active {
  background-color: #009EF7 !important;
  border-color: #009EF7 !important;
  color: #FFFFFF !important;
  box-shadow: none !important;
}

.view-toggle-btn.active:hover {
  background-color: #0084d4 !important;
  border-color: #0084d4 !important;
  color: #FFFFFF !important;
}

/* 统计卡片现代化样式 */
.stat-card-modern {
  background: #ffffff;
  border-radius: 12px;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.stat-card-modern:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px rgba(0,0,0,0.1) !important;
}

.stat-card-modern::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #007bff, #28a745, #dc3545, #17a2b8);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.stat-card-modern:hover::before {
  opacity: 1;
}

.stat-number {
  font-size: 2.5rem;
  font-weight: 700;
  line-height: 1;
  font-family: 'Arial', sans-serif;
}

.stat-label {
  font-size: 0.9rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* 模态框样式 */
.modal-content {
  background-color: #ffffff;
  border: 1px solid #e2e8f0;
  border-radius: var(--kt-border-radius);
  box-shadow: var(--kt-box-shadow);
}

.modal-header {
  background-color: transparent;
  border-bottom: none;
  padding: 1.5rem 1.5rem 0;
}

.modal-title {
  color: var(--kt-text-dark);
  font-weight: 600;
  font-size: 1.125rem;
}

.modal-body {
  padding: 1rem 1.5rem;
  color: var(--kt-text-muted);
}

.modal-footer {
  background-color: transparent;
  border-top: none;
  padding: 0 1.5rem 1.5rem;
}

.alert-warning {
  background-color: #fff8dd;
  border: 1px solid #ffeaa7;
  color: #b8860b;
  border-radius: var(--kt-border-radius-sm);
}

/* 响应式调整 */
@media (max-width: 768px) {
  .d-flex.justify-content-between {
    flex-direction: column;
    gap: 1rem;
  }

  .btn-group {
    width: 100%;
  }

  .btn-group .btn {
    flex: 1;
  }
}
</style>
