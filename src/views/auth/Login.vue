<template>
  <div class="login-page">
    <div class="min-vh-100 d-flex align-items-center bg-gradient-primary">
      <div class="container">
        <div class="row justify-content-center">
          <div class="col-lg-5 col-md-7 col-sm-9">
            <div class="card shadow-lg border-0 rounded-3">
              <div class="card-body p-5">
                <!-- Logo 和标题 -->
                <div class="text-center mb-5">
                  <div class="mb-4">
                    <i class="fas fa-brain fa-3x text-primary"></i>
                  </div>
                  <h1 class="h3 fw-bold text-dark mb-2">智能体矩阵</h1>
                  <p class="text-muted">欢迎回来，请登录您的账户</p>
                </div>

                <!-- 登录表单 -->
                <form @submit.prevent="handleLogin">
                  <div class="mb-4">
                    <label for="email" class="form-label fw-semibold text-dark">邮箱地址</label>
                    <div class="position-relative">
                      <input
                        id="email"
                        v-model="form.email"
                        type="email"
                        class="form-control form-control-lg ps-5"
                        :class="{ 'is-invalid': errors.email }"
                        placeholder="请输入您的邮箱地址"
                        required
                      >
                      <i class="fas fa-envelope position-absolute top-50 start-0 translate-middle-y ms-3 text-muted"></i>
                      <div v-if="errors.email" class="invalid-feedback">
                        {{ errors.email }}
                      </div>
                    </div>
                  </div>

                  <div class="mb-4">
                    <label for="verificationCode" class="form-label fw-semibold text-dark">验证码</label>
                    <div class="input-group">
                      <div class="position-relative flex-grow-1">
                        <input
                          id="verificationCode"
                          v-model="form.verificationCode"
                          type="text"
                          class="form-control form-control-lg ps-5"
                          :class="{ 'is-invalid': errors.verificationCode }"
                          placeholder="请输入6位验证码"
                          maxlength="6"
                          required
                        >
                        <i class="fas fa-key position-absolute top-50 start-0 translate-middle-y ms-3 text-muted"></i>
                      </div>
                      <button
                        type="button"
                        class="btn btn-outline-primary btn-lg px-4"
                        :disabled="!canSendCode || sendingCode"
                        @click="sendVerificationCode"
                      >
                        <span v-if="sendingCode">发送中...</span>
                        <span v-else-if="countdown > 0">{{ countdown }}s后重发</span>
                        <span v-else>发送验证码</span>
                      </button>
                    </div>
                    <div class="form-text mt-2">
                      <i class="fas fa-info-circle me-1"></i>
                      验证码将发送到您的邮箱，有效期5分钟
                    </div>
                    <div v-if="errors.verificationCode" class="invalid-feedback d-block">
                      {{ errors.verificationCode }}
                    </div>
                  </div>

                  <div class="d-grid mb-4">
                    <button
                      type="submit"
                      class="btn btn-primary btn-lg py-3 fw-semibold"
                      :disabled="authStore.isLoading"
                    >
                      <span v-if="authStore.isLoading">
                        <i class="fas fa-spinner fa-spin me-2"></i>
                        登录中...
                      </span>
                      <span v-else>
                        <i class="fas fa-sign-in-alt me-2"></i>
                        立即登录
                      </span>
                    </button>
                  </div>
                </form>

                <!-- 额外信息 -->
                <div class="text-center mt-4">
                  <div class="d-flex align-items-center justify-content-center mb-3">
                    <div class="border-top flex-grow-1"></div>
                    <span class="px-3 text-muted small">或者</span>
                    <div class="border-top flex-grow-1"></div>
                  </div>
                  <p class="text-muted mb-0 small">
                    <i class="fas fa-user-plus me-1"></i>
                    还没有账户？系统将自动为您创建
                  </p>
                </div>

                <!-- 安全提示 -->
                <div class="mt-4 p-3 bg-light rounded-2">
                  <div class="d-flex align-items-start">
                    <i class="fas fa-shield-alt text-success me-2 mt-1"></i>
                    <div>
                      <small class="text-muted">
                        <strong>安全提示：</strong>我们采用邮箱验证码登录方式，确保您的账户安全。
                        请勿将验证码分享给他人。
                      </small>
                    </div>
                  </div>
                </div>




              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { useToast } from 'vue-toastification'
import { isValidEmail } from '@/utils/helpers'

const router = useRouter()
const authStore = useAuthStore()
const toast = useToast()

// 响应式数据
const form = reactive({
  email: '',
  verificationCode: ''
})

const errors = reactive({
  email: '',
  verificationCode: ''
})

const sendingCode = ref(false)
const countdown = ref(0)
let countdownTimer = null

// 计算属性
const canSendCode = computed(() => {
  return isValidEmail(form.email) && countdown.value === 0 && !sendingCode.value
})



// 方法
const validateForm = () => {
  errors.email = ''
  errors.verificationCode = ''
  
  if (!form.email) {
    errors.email = '请输入邮箱地址'
    return false
  }
  
  if (!isValidEmail(form.email)) {
    errors.email = '请输入有效的邮箱地址'
    return false
  }
  
  if (!form.verificationCode) {
    errors.verificationCode = '请输入验证码'
    return false
  }
  
  if (form.verificationCode.length !== 6) {
    errors.verificationCode = '验证码必须是6位数字'
    return false
  }
  
  return true
}

const sendVerificationCode = async () => {
  if (!isValidEmail(form.email)) {
    errors.email = '请输入有效的邮箱地址'
    return
  }
  
  sendingCode.value = true
  
  try {
    const result = await authStore.sendVerificationCode(form.email)
    
    if (result.success) {
      toast.success('验证码发送成功，请查收邮件')
      startCountdown()
    } else {
      toast.error(result.message || '发送验证码失败')
    }
  } catch (error) {
    toast.error('发送验证码失败，请重试')
  } finally {
    sendingCode.value = false
  }
}

const startCountdown = () => {
  countdown.value = 60
  countdownTimer = setInterval(() => {
    countdown.value--
    if (countdown.value <= 0) {
      clearInterval(countdownTimer)
      countdownTimer = null
    }
  }, 1000)
}

const handleLogin = async () => {
  if (!validateForm()) {
    return
  }

  try {
    const result = await authStore.login(form.email, form.verificationCode)

    if (result.success) {
      toast.success('登录成功！')

      // 等待足够长的时间确保会话Cookie完全设置
      console.log('登录成功，等待会话建立...')
      await new Promise(resolve => setTimeout(resolve, 1000))

      // 验证Token是否有效
      try {
        console.log('验证Token状态...')
        const tokenValid = await authStore.checkToken()
        console.log('Token验证结果:', tokenValid)

        if (tokenValid) {
          // Token有效，获取用户信息
          console.log('Token有效，获取用户信息...')
          const userInitialized = await authStore.initUser()
          console.log('用户信息初始化结果:', userInitialized)

          if (userInitialized) {
            console.log('用户信息获取成功，跳转到仪表盘')
            console.log('- 认证状态:', authStore.isAuthenticated)
            console.log('- 用户信息:', authStore.user)

            try {
              await router.push('/dashboard')
              console.log('成功跳转到仪表盘')
            } catch (routerError) {
              console.error('路由跳转失败:', routerError)
              window.location.href = '/dashboard'
            }
          } else {
            console.error('用户信息获取失败')
            toast.error('登录状态异常，请重新登录')
          }
        } else {
          console.error('Token验证失败')
          toast.error('登录状态异常，请重新登录')
        }
      } catch (error) {
        console.error('Token验证异常:', error)
        toast.error('登录状态验证失败，请重新登录')
      }
    } else {
      toast.error(result.message || '登录失败')

      // 如果是验证码错误，清空验证码输入框
      if (result.message?.includes('验证码')) {
        form.verificationCode = ''
      }
    }
  } catch (error) {
    console.error('登录异常:', error)
    toast.error('登录失败，请重试')
  }
}

// 生命周期
onUnmounted(() => {
  if (countdownTimer) {
    clearInterval(countdownTimer)
  }
})
</script>

<style scoped>
/* 登录页面样式 - 与HTML版本保持一致 */
.login-page {
  min-height: 100vh;
}

.bg-gradient-primary {
  background: linear-gradient(135deg, var(--kt-primary) 0%, var(--kt-info) 100%);
  position: relative;
  overflow: hidden;
}

.bg-gradient-primary::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="dots" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23dots)"/></svg>');
  opacity: 0.3;
}

.card {
  backdrop-filter: blur(10px);
  background: rgba(255, 255, 255, 0.95);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: var(--kt-border-radius);
  box-shadow: var(--kt-box-shadow);
}

.form-control-lg {
  padding: 0.875rem 1rem;
  font-size: 1rem;
  border-radius: var(--kt-border-radius);
  border: 2px solid #eff2f5;
  transition: all 0.2s ease;
}

.form-control-lg.ps-5 {
  padding-left: 3rem;
}

.form-control:focus {
  border-color: var(--kt-primary);
  box-shadow: 0 0 0 0.2rem rgba(0, 158, 247, 0.15);
  background-color: #ffffff;
}

.btn-primary {
  background: linear-gradient(135deg, var(--kt-primary) 0%, #0085d1 100%);
  border: none;
  font-weight: 600;
  border-radius: var(--kt-border-radius);
  transition: all 0.2s ease;
  box-shadow: 0 4px 12px rgba(0, 158, 247, 0.3);
}

.btn-primary:hover {
  background: linear-gradient(135deg, #0085d1 0%, #007bb5 100%);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 158, 247, 0.4);
}

.btn-outline-primary {
  border: 2px solid var(--kt-primary);
  color: var(--kt-primary);
  font-weight: 500;
  background: transparent;
  border-radius: var(--kt-border-radius);
  transition: all 0.2s ease;
}

.btn-outline-primary:hover {
  background: var(--kt-primary);
  border-color: var(--kt-primary);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 158, 247, 0.3);
}

.btn-outline-primary:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}



.text-primary {
  color: var(--kt-primary) !important;
}

.text-primary:hover {
  color: var(--kt-primary-dark) !important;
}

@media (max-width: 576px) {
  .card-body {
    padding: 2rem !important;
  }
  
  .h3 {
    font-size: 1.5rem;
  }
}
</style>
