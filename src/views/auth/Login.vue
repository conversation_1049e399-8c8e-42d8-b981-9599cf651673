<template>
  <div class="login-page">
    <div class="min-vh-100 d-flex align-items-center bg-gradient-primary">
      <div class="container">
        <div class="row justify-content-center">
          <div class="col-lg-5 col-md-7 col-sm-9">
            <div class="card shadow-lg border-0 rounded-3">
              <div class="card-body p-5">
                <!-- Logo 和标题 -->
                <div class="text-center mb-5">
                  <div class="mb-4">
                    <i class="fas fa-brain fa-3x text-primary"></i>
                  </div>
                  <h1 class="h3 fw-bold text-dark mb-2">智能体矩阵</h1>
                  <p class="text-muted">欢迎回来，请登录您的账户</p>
                </div>

                <!-- 登录表单 -->
                <form @submit.prevent="handleLogin">
                  <div class="mb-4">
                    <label for="email" class="form-label fw-semibold text-dark">邮箱地址</label>
                    <div class="position-relative">
                      <input
                        id="email"
                        v-model="form.email"
                        type="email"
                        class="form-control form-control-lg ps-5"
                        :class="{ 'is-invalid': errors.email }"
                        placeholder="请输入您的邮箱地址"
                        required
                      >
                      <i class="fas fa-envelope position-absolute top-50 start-0 translate-middle-y ms-3 text-muted"></i>
                      <div v-if="errors.email" class="invalid-feedback">
                        {{ errors.email }}
                      </div>
                    </div>
                  </div>



                  <div class="mb-4">
                    <label for="verificationCode" class="form-label fw-semibold text-dark">邮箱验证码</label>
                    <div class="input-group">
                      <div class="position-relative flex-grow-1">
                        <input
                          id="verificationCode"
                          v-model="form.verificationCode"
                          type="text"
                          class="form-control form-control-lg ps-5"
                          :class="{ 'is-invalid': errors.verificationCode }"
                          placeholder="请输入6位验证码"
                          maxlength="6"
                          required
                        >
                        <i class="fas fa-key position-absolute top-50 start-0 translate-middle-y ms-3 text-muted"></i>
                      </div>
                      <button
                        type="button"
                        class="btn btn-outline-primary btn-lg px-4"
                        :disabled="!canSendCode || sendingCode"
                        @click="openCaptchaModal"
                      >
                        <span v-if="sendingCode">发送中...</span>
                        <span v-else-if="countdown > 0">{{ countdown }}s后重发</span>
                        <span v-else>发送验证码</span>
                      </button>
                    </div>
                    <div class="form-text mt-2">
                      <i class="fas fa-info-circle me-1"></i>
                      验证码将发送到您的邮箱，有效期5分钟
                    </div>
                    <div v-if="errors.verificationCode" class="invalid-feedback d-block">
                      {{ errors.verificationCode }}
                    </div>
                  </div>

                  <div class="d-grid mb-4">
                    <button
                      type="submit"
                      class="btn btn-primary btn-lg py-3 fw-semibold"
                      :disabled="authStore.isLoading"
                    >
                      <span v-if="authStore.isLoading">
                        <i class="fas fa-spinner fa-spin me-2"></i>
                        登录中...
                      </span>
                      <span v-else>
                        <i class="fas fa-sign-in-alt me-2"></i>
                        立即登录
                      </span>
                    </button>
                  </div>
                </form>

                <!-- 额外信息 -->
                <div class="text-center mt-4">
                  <div class="d-flex align-items-center justify-content-center mb-3">
                    <div class="border-top flex-grow-1"></div>
                    <span class="px-3 text-muted small">或者</span>
                    <div class="border-top flex-grow-1"></div>
                  </div>
                  <p class="text-muted mb-0 small">
                    <i class="fas fa-user-plus me-1"></i>
                    还没有账户？系统将自动为您创建
                  </p>
                </div>

                <!-- 安全提示 -->
                <div class="mt-4 p-3 bg-light rounded-2">
                  <div class="d-flex align-items-start">
                    <i class="fas fa-shield-alt text-success me-2 mt-1"></i>
                    <div>
                      <small class="text-muted">
                        <strong>安全提示：</strong>我们采用邮箱验证码登录方式，确保您的账户安全。
                        请勿将验证码分享给他人。
                      </small>
                    </div>
                  </div>
                </div>




              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 图形验证码对话框 -->
    <div
      v-if="showCaptchaModal"
      class="modal fade show d-block"
      tabindex="-1"
      style="background-color: rgba(0, 0, 0, 0.5);"
      @click.self="closeCaptchaModal"
    >
      <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
          <div class="modal-header border-0 pb-0">
            <h5 class="modal-title fw-bold text-dark">
              <i class="fas fa-shield-alt text-primary me-2"></i>
              安全验证
            </h5>
            <button
              type="button"
              class="btn-close"
              @click="closeCaptchaModal"
              aria-label="Close"
            ></button>
          </div>
          <div class="modal-body pt-2">
            <p class="text-muted mb-4">
              <i class="fas fa-info-circle me-1"></i>
              为了您的账户安全，请完成图形验证码验证
            </p>

            <div class="text-center mb-4">
              <div class="captcha-modal-container mx-auto">
                <img
                  v-if="captchaImage"
                  :src="captchaImage"
                  alt="图形验证码"
                  class="captcha-modal-image"
                  @click="refreshCaptcha"
                  :style="{ cursor: loadingCaptcha ? 'not-allowed' : 'pointer' }"
                >
                <div v-else class="captcha-modal-placeholder d-flex align-items-center justify-content-center">
                  <i class="fas fa-spinner fa-spin text-muted"></i>
                </div>
              </div>
              <button
                type="button"
                class="btn btn-outline-secondary btn-sm mt-3"
                @click="refreshCaptcha"
                :disabled="loadingCaptcha"
              >
                <i class="fas fa-sync-alt me-1" :class="{ 'fa-spin': loadingCaptcha }"></i>
                刷新验证码
              </button>
            </div>

            <div class="mb-3">
              <label for="modalCaptchaCode" class="form-label fw-semibold text-dark">
                请输入图形验证码
              </label>
              <input
                id="modalCaptchaCode"
                v-model="modalCaptchaCode"
                type="text"
                class="form-control form-control-lg text-center"
                :class="{ 'is-invalid': modalCaptchaError }"
                placeholder="请输入4位验证码"
                maxlength="4"
                @keyup.enter="confirmSendCode"
                @input="modalCaptchaError = ''"
              >
              <div v-if="modalCaptchaError" class="invalid-feedback">
                {{ modalCaptchaError }}
              </div>
            </div>
          </div>
          <div class="modal-footer border-0 pt-0">
            <button
              type="button"
              class="btn btn-outline-secondary"
              @click="closeCaptchaModal"
            >
              取消
            </button>
            <button
              type="button"
              class="btn btn-primary"
              :disabled="!modalCaptchaCode || modalCaptchaCode.length !== 4 || sendingCode"
              @click="confirmSendCode"
            >
              <span v-if="sendingCode">
                <i class="fas fa-spinner fa-spin me-1"></i>
                发送中...
              </span>
              <span v-else>
                <i class="fas fa-paper-plane me-1"></i>
                确认发送
              </span>
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { useToast } from 'vue-toastification'
import { isValidEmail } from '@/utils/helpers'

const router = useRouter()
const authStore = useAuthStore()
const toast = useToast()

// 响应式数据
const form = reactive({
  email: '',
  verificationCode: ''
})

const errors = reactive({
  email: '',
  verificationCode: ''
})

const sendingCode = ref(false)
const countdown = ref(0)
const captchaImage = ref('')
const captchaId = ref('')
const loadingCaptcha = ref(false)
const showCaptchaModal = ref(false)
const modalCaptchaCode = ref('')
const modalCaptchaError = ref('')
let countdownTimer = null

// 计算属性
const canSendCode = computed(() => {
  return isValidEmail(form.email) &&
         countdown.value === 0 &&
         !sendingCode.value
})



// 方法
const validateForm = () => {
  errors.email = ''
  errors.verificationCode = ''

  if (!form.email) {
    errors.email = '请输入邮箱地址'
    return false
  }

  if (!isValidEmail(form.email)) {
    errors.email = '请输入有效的邮箱地址'
    return false
  }

  if (!form.verificationCode) {
    errors.verificationCode = '请输入验证码'
    return false
  }

  if (form.verificationCode.length !== 6) {
    errors.verificationCode = '验证码必须是6位数字'
    return false
  }

  return true
}

// 获取图形验证码
const getCaptcha = async () => {
  loadingCaptcha.value = true
  try {
    const result = await authStore.getCaptcha()
    if (result.success) {
      captchaImage.value = result.data.captchaImage
      captchaId.value = result.data.captchaId
    } else {
      toast.error(result.message || '获取图形验证码失败')
    }
  } catch (error) {
    toast.error('获取图形验证码失败，请重试')
  } finally {
    loadingCaptcha.value = false
  }
}

// 刷新图形验证码
const refreshCaptcha = () => {
  modalCaptchaCode.value = ''
  modalCaptchaError.value = ''
  getCaptcha()
}

// 打开图形验证码对话框
const openCaptchaModal = async () => {
  if (!isValidEmail(form.email)) {
    errors.email = '请输入有效的邮箱地址'
    return
  }

  // 获取图形验证码
  await getCaptcha()
  showCaptchaModal.value = true
  modalCaptchaCode.value = ''
  modalCaptchaError.value = ''

  // 聚焦到输入框
  setTimeout(() => {
    const input = document.getElementById('modalCaptchaCode')
    if (input) input.focus()
  }, 100)
}

// 关闭图形验证码对话框
const closeCaptchaModal = () => {
  showCaptchaModal.value = false
  modalCaptchaCode.value = ''
  modalCaptchaError.value = ''
}

// 确认发送验证码
const confirmSendCode = async () => {
  if (!modalCaptchaCode.value) {
    modalCaptchaError.value = '请输入图形验证码'
    return
  }

  if (modalCaptchaCode.value.length !== 4) {
    modalCaptchaError.value = '图形验证码必须是4位'
    return
  }

  sendingCode.value = true

  try {
    const result = await authStore.sendVerificationCode(form.email, captchaId.value, modalCaptchaCode.value)

    if (result.success) {
      toast.success('验证码发送成功，请查收邮件')
      startCountdown()
      closeCaptchaModal()
    } else {
      modalCaptchaError.value = result.message || '发送验证码失败'
      // 如果是图形验证码错误，刷新验证码
      if (result.message?.includes('图形验证码')) {
        refreshCaptcha()
      }
    }
  } catch (error) {
    modalCaptchaError.value = '发送验证码失败，请重试'
  } finally {
    sendingCode.value = false
  }
}



const startCountdown = () => {
  countdown.value = 60
  countdownTimer = setInterval(() => {
    countdown.value--
    if (countdown.value <= 0) {
      clearInterval(countdownTimer)
      countdownTimer = null
    }
  }, 1000)
}

const handleLogin = async () => {
  if (!validateForm()) {
    return
  }

  try {
    const result = await authStore.login(form.email, form.verificationCode)

    if (result.success) {
      toast.success('登录成功！')

      // 等待足够长的时间确保会话Cookie完全设置
      console.log('登录成功，等待会话建立...')
      await new Promise(resolve => setTimeout(resolve, 1000))

      // 验证Token是否有效
      try {
        console.log('验证Token状态...')
        const tokenValid = await authStore.checkToken()
        console.log('Token验证结果:', tokenValid)

        if (tokenValid) {
          // Token有效，获取用户信息
          console.log('Token有效，获取用户信息...')
          const userInitialized = await authStore.initUser()
          console.log('用户信息初始化结果:', userInitialized)

          if (userInitialized) {
            console.log('用户信息获取成功，跳转到仪表盘')
            console.log('- 认证状态:', authStore.isAuthenticated)
            console.log('- 用户信息:', authStore.user)

            try {
              await router.push('/dashboard')
              console.log('成功跳转到仪表盘')
            } catch (routerError) {
              console.error('路由跳转失败:', routerError)
              window.location.href = '/dashboard'
            }
          } else {
            console.error('用户信息获取失败')
            toast.error('登录状态异常，请重新登录')
          }
        } else {
          console.error('Token验证失败')
          toast.error('登录状态异常，请重新登录')
        }
      } catch (error) {
        console.error('Token验证异常:', error)
        toast.error('登录状态验证失败，请重新登录')
      }
    } else {
      toast.error(result.message || '登录失败')

      // 如果是验证码错误，清空验证码输入框
      if (result.message?.includes('验证码')) {
        form.verificationCode = ''
      }
    }
  } catch (error) {
    console.error('登录异常:', error)
    toast.error('登录失败，请重试')
  }
}

// 生命周期
onMounted(() => {
  // 页面加载时不再自动获取图形验证码，改为在打开对话框时获取
})

onUnmounted(() => {
  if (countdownTimer) {
    clearInterval(countdownTimer)
  }
})
</script>

<style scoped>
/* 登录页面样式 - 与HTML版本保持一致 */
.login-page {
  min-height: 100vh;
}

.bg-gradient-primary {
  background: linear-gradient(135deg, var(--kt-primary) 0%, var(--kt-info) 100%);
  position: relative;
  overflow: hidden;
}

.bg-gradient-primary::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="dots" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23dots)"/></svg>');
  opacity: 0.3;
}

.card {
  backdrop-filter: blur(10px);
  background: rgba(255, 255, 255, 0.95);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: var(--kt-border-radius);
  box-shadow: var(--kt-box-shadow);
}

.form-control-lg {
  padding: 0.875rem 1rem;
  font-size: 1rem;
  border-radius: var(--kt-border-radius);
  border: 2px solid #e8ecef;
  transition: all 0.3s ease;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(5px);
}

.form-control-lg.ps-5 {
  padding-left: 3rem;
}

.form-control:focus {
  border-color: var(--kt-primary);
  box-shadow: 0 0 0 0.2rem rgba(0, 158, 247, 0.15);
  background-color: #ffffff;
  transform: translateY(-1px);
}



.btn-primary {
  background: linear-gradient(135deg, var(--kt-primary) 0%, #0085d1 100%);
  border: none;
  font-weight: 600;
  border-radius: var(--kt-border-radius);
  transition: all 0.2s ease;
  box-shadow: 0 4px 12px rgba(0, 158, 247, 0.3);
}

.btn-primary:hover {
  background: linear-gradient(135deg, #0085d1 0%, #007bb5 100%);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 158, 247, 0.4);
}

.btn-outline-primary {
  border: 2px solid var(--kt-primary);
  color: var(--kt-primary);
  font-weight: 500;
  background: transparent;
  border-radius: var(--kt-border-radius);
  transition: all 0.2s ease;
}

.btn-outline-primary:hover {
  background: var(--kt-primary);
  border-color: var(--kt-primary);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 158, 247, 0.3);
}

.btn-outline-primary:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* 图形验证码对话框样式 */
.modal {
  z-index: 1055;
}

.modal-content {
  border: none;
  border-radius: var(--kt-border-radius);
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.2);
  backdrop-filter: blur(10px);
  background: rgba(255, 255, 255, 0.98);
}

.modal-header {
  padding: 1.5rem 1.5rem 0.5rem;
}

.modal-body {
  padding: 0.5rem 1.5rem;
}

.modal-footer {
  padding: 0.5rem 1.5rem 1.5rem;
}

.captcha-modal-container {
  width: 160px;
  height: 64px;
  border-radius: var(--kt-border-radius);
  overflow: hidden;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  transition: all 0.3s ease;
}

.captcha-modal-container:hover {
  box-shadow: 0 6px 24px rgba(0, 158, 247, 0.25);
  transform: translateY(-2px);
}

.captcha-modal-image {
  width: 100%;
  height: 100%;
  border: 2px solid #e8ecef;
  border-radius: var(--kt-border-radius);
  cursor: pointer;
  transition: all 0.3s ease;
  object-fit: cover;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.captcha-modal-image:hover {
  border-color: var(--kt-primary);
  box-shadow: 0 0 0 0.2rem rgba(0, 158, 247, 0.15);
}

.captcha-modal-placeholder {
  width: 100%;
  height: 100%;
  border: 2px dashed #dee2e6;
  border-radius: var(--kt-border-radius);
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  color: #6c757d;
  font-size: 1.2rem;
}

/* 对话框中的输入框样式 */
#modalCaptchaCode {
  font-weight: 600;
  letter-spacing: 0.3em;
  text-transform: uppercase;
  font-size: 1.1rem;
}



.text-primary {
  color: var(--kt-primary) !important;
}

.text-primary:hover {
  color: var(--kt-primary-dark) !important;
}

/* 响应式设计 */
@media (max-width: 576px) {
  .card-body {
    padding: 2rem !important;
  }

  .h3 {
    font-size: 1.5rem;
  }

  .modal-dialog {
    margin: 1rem;
  }

  .captcha-modal-container {
    width: 140px;
    height: 56px;
  }

  .modal-header,
  .modal-body,
  .modal-footer {
    padding-left: 1rem;
    padding-right: 1rem;
  }
}
</style>
