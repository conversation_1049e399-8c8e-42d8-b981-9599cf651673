<template>
  <div class="login-page">
    <div class="min-vh-100 d-flex align-items-center bg-gradient-primary">
      <div class="container">
        <div class="row justify-content-center">
          <div class="col-lg-5 col-md-7 col-sm-9">
            <div class="card shadow-lg border-0 rounded-3">
              <div class="card-body p-5">
                <!-- Logo 和标题 -->
                <div class="text-center mb-5">
                  <div class="mb-4">
                    <i class="fas fa-brain fa-3x text-primary"></i>
                  </div>
                  <h1 class="h3 fw-bold text-dark mb-2">智能体矩阵</h1>
                  <p class="text-muted">欢迎回来，请登录您的账户</p>
                </div>

                <!-- 登录表单 -->
                <form @submit.prevent="handleLogin">
                  <div class="mb-4">
                    <label for="email" class="form-label fw-semibold text-dark">邮箱地址</label>
                    <div class="position-relative">
                      <input
                        id="email"
                        v-model="form.email"
                        type="email"
                        class="form-control form-control-lg ps-5"
                        :class="{ 'is-invalid': errors.email }"
                        placeholder="请输入您的邮箱地址"
                        required
                      >
                      <i class="fas fa-envelope position-absolute top-50 start-0 translate-middle-y ms-3 text-muted"></i>
                      <div v-if="errors.email" class="invalid-feedback">
                        {{ errors.email }}
                      </div>
                    </div>
                  </div>

                  <!-- 图形验证码 -->
                  <div class="mb-4">
                    <label for="captchaCode" class="form-label fw-semibold text-dark">图形验证码</label>
                    <div class="d-flex gap-3 align-items-start">
                      <div class="flex-grow-1">
                        <div class="position-relative">
                          <input
                            id="captchaCode"
                            v-model="form.captchaCode"
                            type="text"
                            class="form-control form-control-lg ps-5"
                            :class="{ 'is-invalid': errors.captchaCode }"
                            placeholder="请输入4位验证码"
                            maxlength="4"
                            required
                          >
                          <i class="fas fa-shield-alt position-absolute top-50 start-0 translate-middle-y ms-3 text-muted"></i>
                        </div>
                        <div v-if="errors.captchaCode" class="invalid-feedback d-block">
                          {{ errors.captchaCode }}
                        </div>
                      </div>
                      <div class="captcha-section">
                        <div class="captcha-wrapper">
                          <div class="captcha-container">
                            <img
                              v-if="captchaImage"
                              :src="captchaImage"
                              alt="图形验证码"
                              class="captcha-image"
                              @click="refreshCaptcha"
                              :style="{ cursor: loadingCaptcha ? 'not-allowed' : 'pointer' }"
                            >
                            <div v-else class="captcha-placeholder d-flex align-items-center justify-content-center">
                              <i class="fas fa-spinner fa-spin text-muted"></i>
                            </div>
                          </div>
                          <button
                            type="button"
                            class="btn btn-refresh mt-2"
                            @click="refreshCaptcha"
                            :disabled="loadingCaptcha"
                            title="刷新验证码"
                          >
                            <i class="fas fa-sync-alt me-1" :class="{ 'fa-spin': loadingCaptcha }"></i>
                            <span class="small">刷新</span>
                          </button>
                        </div>
                      </div>
                    </div>
                    <div class="form-text mt-2">
                      <i class="fas fa-info-circle me-1"></i>
                      点击图片或按钮可刷新验证码
                    </div>
                  </div>

                  <div class="mb-4">
                    <label for="verificationCode" class="form-label fw-semibold text-dark">邮箱验证码</label>
                    <div class="input-group">
                      <div class="position-relative flex-grow-1">
                        <input
                          id="verificationCode"
                          v-model="form.verificationCode"
                          type="text"
                          class="form-control form-control-lg ps-5"
                          :class="{ 'is-invalid': errors.verificationCode }"
                          placeholder="请输入6位验证码"
                          maxlength="6"
                          required
                        >
                        <i class="fas fa-key position-absolute top-50 start-0 translate-middle-y ms-3 text-muted"></i>
                      </div>
                      <button
                        type="button"
                        class="btn btn-outline-primary btn-lg px-4"
                        :disabled="!canSendCode || sendingCode"
                        @click="sendVerificationCode"
                      >
                        <span v-if="sendingCode">发送中...</span>
                        <span v-else-if="countdown > 0">{{ countdown }}s后重发</span>
                        <span v-else>发送验证码</span>
                      </button>
                    </div>
                    <div class="form-text mt-2">
                      <i class="fas fa-info-circle me-1"></i>
                      验证码将发送到您的邮箱，有效期5分钟
                    </div>
                    <div v-if="errors.verificationCode" class="invalid-feedback d-block">
                      {{ errors.verificationCode }}
                    </div>
                  </div>

                  <div class="d-grid mb-4">
                    <button
                      type="submit"
                      class="btn btn-primary btn-lg py-3 fw-semibold"
                      :disabled="authStore.isLoading"
                    >
                      <span v-if="authStore.isLoading">
                        <i class="fas fa-spinner fa-spin me-2"></i>
                        登录中...
                      </span>
                      <span v-else>
                        <i class="fas fa-sign-in-alt me-2"></i>
                        立即登录
                      </span>
                    </button>
                  </div>
                </form>

                <!-- 额外信息 -->
                <div class="text-center mt-4">
                  <div class="d-flex align-items-center justify-content-center mb-3">
                    <div class="border-top flex-grow-1"></div>
                    <span class="px-3 text-muted small">或者</span>
                    <div class="border-top flex-grow-1"></div>
                  </div>
                  <p class="text-muted mb-0 small">
                    <i class="fas fa-user-plus me-1"></i>
                    还没有账户？系统将自动为您创建
                  </p>
                </div>

                <!-- 安全提示 -->
                <div class="mt-4 p-3 bg-light rounded-2">
                  <div class="d-flex align-items-start">
                    <i class="fas fa-shield-alt text-success me-2 mt-1"></i>
                    <div>
                      <small class="text-muted">
                        <strong>安全提示：</strong>我们采用邮箱验证码登录方式，确保您的账户安全。
                        请勿将验证码分享给他人。
                      </small>
                    </div>
                  </div>
                </div>




              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { useToast } from 'vue-toastification'
import { isValidEmail } from '@/utils/helpers'

const router = useRouter()
const authStore = useAuthStore()
const toast = useToast()

// 响应式数据
const form = reactive({
  email: '',
  verificationCode: '',
  captchaCode: ''
})

const errors = reactive({
  email: '',
  verificationCode: '',
  captchaCode: ''
})

const sendingCode = ref(false)
const countdown = ref(0)
const captchaImage = ref('')
const captchaId = ref('')
const loadingCaptcha = ref(false)
let countdownTimer = null

// 计算属性
const canSendCode = computed(() => {
  return isValidEmail(form.email) &&
         form.captchaCode.length === 4 &&
         countdown.value === 0 &&
         !sendingCode.value &&
         captchaId.value
})



// 方法
const validateForm = () => {
  errors.email = ''
  errors.verificationCode = ''
  errors.captchaCode = ''

  if (!form.email) {
    errors.email = '请输入邮箱地址'
    return false
  }

  if (!isValidEmail(form.email)) {
    errors.email = '请输入有效的邮箱地址'
    return false
  }

  if (!form.verificationCode) {
    errors.verificationCode = '请输入验证码'
    return false
  }

  if (form.verificationCode.length !== 6) {
    errors.verificationCode = '验证码必须是6位数字'
    return false
  }

  return true
}

// 获取图形验证码
const getCaptcha = async () => {
  loadingCaptcha.value = true
  try {
    const result = await authStore.getCaptcha()
    if (result.success) {
      captchaImage.value = result.data.captchaImage
      captchaId.value = result.data.captchaId
    } else {
      toast.error(result.message || '获取图形验证码失败')
    }
  } catch (error) {
    toast.error('获取图形验证码失败，请重试')
  } finally {
    loadingCaptcha.value = false
  }
}

// 刷新图形验证码
const refreshCaptcha = () => {
  form.captchaCode = ''
  getCaptcha()
}

const sendVerificationCode = async () => {
  if (!isValidEmail(form.email)) {
    errors.email = '请输入有效的邮箱地址'
    return
  }

  if (!form.captchaCode) {
    errors.captchaCode = '请输入图形验证码'
    return
  }

  if (form.captchaCode.length !== 4) {
    errors.captchaCode = '图形验证码必须是4位'
    return
  }

  sendingCode.value = true

  try {
    const result = await authStore.sendVerificationCode(form.email, captchaId.value, form.captchaCode)

    if (result.success) {
      toast.success('验证码发送成功，请查收邮件')
      startCountdown()
      // 发送成功后刷新图形验证码
      refreshCaptcha()
    } else {
      toast.error(result.message || '发送验证码失败')
      // 如果是图形验证码错误，刷新验证码
      if (result.message?.includes('图形验证码')) {
        refreshCaptcha()
      }
    }
  } catch (error) {
    toast.error('发送验证码失败，请重试')
  } finally {
    sendingCode.value = false
  }
}

const startCountdown = () => {
  countdown.value = 60
  countdownTimer = setInterval(() => {
    countdown.value--
    if (countdown.value <= 0) {
      clearInterval(countdownTimer)
      countdownTimer = null
    }
  }, 1000)
}

const handleLogin = async () => {
  if (!validateForm()) {
    return
  }

  try {
    const result = await authStore.login(form.email, form.verificationCode)

    if (result.success) {
      toast.success('登录成功！')

      // 等待足够长的时间确保会话Cookie完全设置
      console.log('登录成功，等待会话建立...')
      await new Promise(resolve => setTimeout(resolve, 1000))

      // 验证Token是否有效
      try {
        console.log('验证Token状态...')
        const tokenValid = await authStore.checkToken()
        console.log('Token验证结果:', tokenValid)

        if (tokenValid) {
          // Token有效，获取用户信息
          console.log('Token有效，获取用户信息...')
          const userInitialized = await authStore.initUser()
          console.log('用户信息初始化结果:', userInitialized)

          if (userInitialized) {
            console.log('用户信息获取成功，跳转到仪表盘')
            console.log('- 认证状态:', authStore.isAuthenticated)
            console.log('- 用户信息:', authStore.user)

            try {
              await router.push('/dashboard')
              console.log('成功跳转到仪表盘')
            } catch (routerError) {
              console.error('路由跳转失败:', routerError)
              window.location.href = '/dashboard'
            }
          } else {
            console.error('用户信息获取失败')
            toast.error('登录状态异常，请重新登录')
          }
        } else {
          console.error('Token验证失败')
          toast.error('登录状态异常，请重新登录')
        }
      } catch (error) {
        console.error('Token验证异常:', error)
        toast.error('登录状态验证失败，请重新登录')
      }
    } else {
      toast.error(result.message || '登录失败')

      // 如果是验证码错误，清空验证码输入框
      if (result.message?.includes('验证码')) {
        form.verificationCode = ''
      }
    }
  } catch (error) {
    console.error('登录异常:', error)
    toast.error('登录失败，请重试')
  }
}

// 生命周期
onMounted(() => {
  // 页面加载时获取图形验证码
  getCaptcha()
})

onUnmounted(() => {
  if (countdownTimer) {
    clearInterval(countdownTimer)
  }
})
</script>

<style scoped>
/* 登录页面样式 - 与HTML版本保持一致 */
.login-page {
  min-height: 100vh;
}

.bg-gradient-primary {
  background: linear-gradient(135deg, var(--kt-primary) 0%, var(--kt-info) 100%);
  position: relative;
  overflow: hidden;
}

.bg-gradient-primary::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="dots" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23dots)"/></svg>');
  opacity: 0.3;
}

.card {
  backdrop-filter: blur(10px);
  background: rgba(255, 255, 255, 0.95);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: var(--kt-border-radius);
  box-shadow: var(--kt-box-shadow);
}

.form-control-lg {
  padding: 0.875rem 1rem;
  font-size: 1rem;
  border-radius: var(--kt-border-radius);
  border: 2px solid #e8ecef;
  transition: all 0.3s ease;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(5px);
}

.form-control-lg.ps-5 {
  padding-left: 3rem;
}

.form-control:focus {
  border-color: var(--kt-primary);
  box-shadow: 0 0 0 0.2rem rgba(0, 158, 247, 0.15);
  background-color: #ffffff;
  transform: translateY(-1px);
}

/* 图形验证码输入框特殊样式 */
#captchaCode.form-control-lg {
  text-align: center;
  font-weight: 600;
  letter-spacing: 0.2em;
  text-transform: uppercase;
}

#captchaCode.form-control-lg.ps-5 {
  padding-left: 3rem;
  text-align: left;
}

.btn-primary {
  background: linear-gradient(135deg, var(--kt-primary) 0%, #0085d1 100%);
  border: none;
  font-weight: 600;
  border-radius: var(--kt-border-radius);
  transition: all 0.2s ease;
  box-shadow: 0 4px 12px rgba(0, 158, 247, 0.3);
}

.btn-primary:hover {
  background: linear-gradient(135deg, #0085d1 0%, #007bb5 100%);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 158, 247, 0.4);
}

.btn-outline-primary {
  border: 2px solid var(--kt-primary);
  color: var(--kt-primary);
  font-weight: 500;
  background: transparent;
  border-radius: var(--kt-border-radius);
  transition: all 0.2s ease;
}

.btn-outline-primary:hover {
  background: var(--kt-primary);
  border-color: var(--kt-primary);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 158, 247, 0.3);
}

.btn-outline-primary:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* 图形验证码样式 */
.captcha-section {
  min-width: 130px;
}

.captcha-wrapper {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.captcha-container {
  width: 120px;
  height: 48px;
  border-radius: var(--kt-border-radius);
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.captcha-container:hover {
  box-shadow: 0 4px 16px rgba(0, 158, 247, 0.2);
  transform: translateY(-1px);
}

.captcha-image {
  width: 100%;
  height: 100%;
  border: 2px solid #e8ecef;
  border-radius: var(--kt-border-radius);
  cursor: pointer;
  transition: all 0.3s ease;
  object-fit: cover;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.captcha-image:hover {
  border-color: var(--kt-primary);
  box-shadow: 0 0 0 0.2rem rgba(0, 158, 247, 0.15);
}

.captcha-placeholder {
  width: 100%;
  height: 100%;
  border: 2px dashed #dee2e6;
  border-radius: var(--kt-border-radius);
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  color: #6c757d;
}

.btn-refresh {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border: 1px solid #dee2e6;
  color: #495057;
  font-size: 0.75rem;
  padding: 0.375rem 0.75rem;
  border-radius: var(--kt-border-radius);
  transition: all 0.3s ease;
  min-width: 60px;
}

.btn-refresh:hover {
  background: linear-gradient(135deg, #e9ecef 0%, #dee2e6 100%);
  border-color: var(--kt-primary);
  color: var(--kt-primary);
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 158, 247, 0.15);
}

.btn-refresh:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.btn-refresh:disabled:hover {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-color: #dee2e6;
  color: #495057;
  box-shadow: none;
}



.text-primary {
  color: var(--kt-primary) !important;
}

.text-primary:hover {
  color: var(--kt-primary-dark) !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .captcha-section {
    min-width: 110px;
  }

  .captcha-container {
    width: 110px;
    height: 44px;
  }

  .btn-refresh {
    font-size: 0.7rem;
    padding: 0.25rem 0.5rem;
    min-width: 50px;
  }
}

@media (max-width: 576px) {
  .card-body {
    padding: 2rem !important;
  }

  .h3 {
    font-size: 1.5rem;
  }

  .captcha-section {
    min-width: 100px;
  }

  .captcha-container {
    width: 100px;
    height: 40px;
  }

  .d-flex.gap-3 {
    gap: 1rem !important;
  }

  .btn-refresh {
    font-size: 0.65rem;
    padding: 0.2rem 0.4rem;
    min-width: 45px;
  }

  .btn-refresh .small {
    display: none;
  }
}
</style>
