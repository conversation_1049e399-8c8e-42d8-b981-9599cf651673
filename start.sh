#!/bin/bash

echo "========================================"
echo "SINOAIR-AGENT Vue.js 前端启动脚本"
echo "========================================"
echo

# 解析命令行参数
ENV_MODE="development"
case "$1" in
    "test")
        ENV_MODE="test"
        ;;
    "staging")
        ENV_MODE="staging"
        ;;
    "prod"|"production")
        ENV_MODE="production"
        ;;
esac

echo "[信息] 启动环境: $ENV_MODE"
echo

# 检查 Node.js 是否安装
if ! command -v node &> /dev/null; then
    echo "[错误] 未检测到 Node.js，请先安装 Node.js"
    echo "下载地址: https://nodejs.org/"
    exit 1
fi

echo "[信息] Node.js 版本:"
node --version
echo

# 检查是否存在 node_modules
if [ ! -d "node_modules" ]; then
    echo "[信息] 首次运行，正在安装依赖..."
    echo
    npm install
    if [ $? -ne 0 ]; then
        echo "[错误] 依赖安装失败"
        exit 1
    fi
    echo
    echo "[成功] 依赖安装完成"
    echo
fi

# 检查环境配置文件
if [ ! -f ".env.local" ]; then
    if [ -f ".env.example" ]; then
        echo "[信息] 创建环境配置文件..."
        cp ".env.example" ".env.local"
        echo "[成功] 已创建 .env.local 文件"
        echo
    fi
fi

# 启动开发服务器
echo "[信息] 启动开发服务器..."
echo "[信息] 环境模式: $ENV_MODE"
echo "[信息] 按 Ctrl+C 停止服务器"
echo
echo "========================================"
echo

case "$ENV_MODE" in
    "development")
        npm run dev
        ;;
    "test")
        npm run dev:test
        ;;
    "staging")
        npm run dev:staging
        ;;
    "production")
        npm run dev:prod
        ;;
esac
