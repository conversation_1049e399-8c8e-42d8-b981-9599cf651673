/* Dark Header Layout - 参照 Keen 主题的 Dark Header 布局 */

/* Header 样式 */
.header {
    padding: 10px;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1030;
    height: var(--kt-header-height);
    background-color: var(--kt-header-bg);
    border-bottom: 1px solid var(--kt-header-border-color);
    box-shadow: 0 0.5rem 1.5rem 0.5rem rgba(0, 0, 0, 0.075);
    transition: var(--kt-transition);
}

.header-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 100%;
    padding: 0 1.5rem;
}

/* Logo 和品牌 */
.header-logo {
    display: flex;
    align-items: center;
    text-decoration: none;
    color: #ffffff;
    font-size: 1.5rem;
    font-weight: var(--kt-font-weight-bold);
    transition: var(--kt-transition);
}

.header-logo:hover {
    color: var(--kt-primary);
    text-decoration: none;
}

.header-logo img {
    height: 35px;
    margin-right: 0.75rem;
}

/* 导航菜单 - 优化布局和视觉效果 */
.header-menu {
    display: flex;
    align-items: center;
    list-style: none;
    margin: 0;
    padding: 0;
}

.header-menu-item {
    position: relative;
    margin: 0 0.25rem;
}

.header-menu-link {
    display: flex;
    align-items: center;
    padding: 0.75rem 1.25rem;
    color: #cbd5e1;
    text-decoration: none;
    font-weight: var(--kt-font-weight-semibold);
    font-size: 0.9rem;
    border-radius: var(--kt-border-radius-sm);
    transition: all 0.3s ease;
    position: relative;
    white-space: nowrap;
}

.header-menu-link:hover {
    color: #ffffff;
    background-color: rgba(255, 255, 255, 0.12);
    text-decoration: none;
    transform: translateY(-1px);
}

.header-menu-link.active {
    color: var(--kt-primary);
    background-color: rgba(0, 158, 247, 0.15);
}

.header-menu-link.active::after {
    content: '';
    position: absolute;
    bottom: -1px;
    left: 1.25rem;
    right: 1.25rem;
    height: 2px;
    background-color: var(--kt-primary);
    border-radius: 1px;
}

.header-menu-link i {
    font-size: 0.85rem;
    opacity: 0.8;
}

.header-menu-link:hover i {
    opacity: 1;
}

/* 用户菜单 */
.header-user-menu {
    position: relative;
}

.header-user-menu .dropdown-toggle {
    display: flex;
    align-items: center;
    padding: 0.5rem;
    color: #ffffff;
    text-decoration: none;
    border-radius: var(--kt-border-radius-sm);
    transition: var(--kt-transition);
    border: none;
    background: none;
}

.header-user-menu .dropdown-toggle:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

.header-user-menu .dropdown-toggle::after {
    margin-left: 0.5rem;
    border-top-color: #a1a5b7;
}

.header-user-avatar {
    width: 35px;
    height: 35px;
    border-radius: 50%;
    margin-right: 0.75rem;
    border: 2px solid rgba(255, 255, 255, 0.2);
}

/* 文字头像样式 */
.user-avatar-text {
    background: linear-gradient(135deg, #009ef7 0%, #4facfe 100%);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.875rem;
    font-weight: 600;
    text-transform: uppercase;
    transition: all 0.3s ease;
}

.user-avatar-text:hover {
    transform: scale(1.05);
    box-shadow: 0 2px 8px rgba(0, 158, 247, 0.3);
}

.header-user-info {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
}

.header-user-name {
    font-size: 0.875rem;
    font-weight: var(--kt-font-weight-semibold);
    line-height: 1.2;
    margin: 0;
}

.header-user-email {
    font-size: 0.75rem;
    color: #a1a5b7;
    line-height: 1.2;
    margin: 0;
}

/* 下拉菜单样式 */
.dropdown-menu {
    min-width: 250px;
    padding: 0.5rem 0;
    margin: 0.5rem 0 0;
    background-color: #ffffff;
    border: 1px solid var(--kt-border-color);
    border-radius: var(--kt-border-radius);
    box-shadow: var(--kt-box-shadow);
}

.dropdown-item {
    display: flex;
    align-items: center;
    width: 100%;
    padding: 0.75rem 1.5rem;
    clear: both;
    font-weight: var(--kt-font-weight-normal);
    color: var(--kt-text-gray-700);
    text-align: inherit;
    text-decoration: none;
    white-space: nowrap;
    background-color: transparent;
    border: 0;
    transition: var(--kt-transition);
}

.dropdown-item:hover {
    color: var(--kt-text-dark);
    background-color: var(--kt-light);
}

.dropdown-item i {
    margin-right: 0.75rem;
    font-size: 1rem;
    color: var(--kt-text-muted);
}

.dropdown-divider {
    height: 0;
    margin: 0.5rem 0;
    overflow: hidden;
    border-top: 1px solid var(--kt-border-color);
}

/* 主内容区域 */
.main-content {
    margin-top: var(--kt-header-height);
    background-color: var(--kt-body-bg);
    padding-bottom: 2rem;
}

/* 工具栏 */
.toolbar {
    background-color: var(--kt-toolbar-bg);
    border-bottom: 1px solid var(--kt-border-color);
    padding: 1rem 0;
    margin-bottom: 2rem;
}

.toolbar-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.toolbar-title {
    margin: 0;
    font-size: 1.5rem;
    font-weight: var(--kt-font-weight-semibold);
    color: var(--kt-text-dark);
}

.toolbar-actions {
    display: flex;
    align-items: center;
    gap: 1rem;
}

/* 面包屑导航 */
.breadcrumb {
    display: flex;
    flex-wrap: wrap;
    padding: 0;
    margin: 0;
    list-style: none;
    background-color: transparent;
}

.breadcrumb-item {
    display: flex;
    align-items: center;
}

.breadcrumb-item + .breadcrumb-item::before {
    content: "/";
    padding: 0 0.5rem;
    color: var(--kt-text-muted);
}

.breadcrumb-item a {
    color: var(--kt-text-muted);
    text-decoration: none;
    transition: var(--kt-transition);
}

.breadcrumb-item a:hover {
    color: var(--kt-primary);
}

.breadcrumb-item.active {
    color: var(--kt-text-gray-700);
}

/* 移动端适配 - 优化响应式布局 */
@media (max-width: 991.98px) {
    .header-menu {
        display: none;
    }

    .header-container {
        padding: 0 1rem;
        justify-content: space-between;
    }

    .header-logo {
        font-size: 1.25rem;
    }

    .toolbar-container {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }

    .toolbar-title {
        font-size: 1.25rem;
    }
}

@media (max-width: 575.98px) {
    .header-user-info {
        display: none;
    }

    .header-user-avatar {
        margin-right: 0;
        width: 32px;
        height: 32px;
    }

    .user-avatar-text {
        font-size: 0.75rem;
    }

    .dropdown-menu {
        min-width: 200px;
        right: 0;
        left: auto;
    }

    .header-container {
        padding: 0 0.75rem;
    }

    .header-logo {
        font-size: 1.1rem;
    }

    .header-logo i {
        font-size: 1rem;
    }

    .btn-sm {
        padding: 0.4rem 0.8rem;
        font-size: 0.8rem;
    }
}

/* 移动端菜单切换按钮 */
.header-menu-toggle {
    display: none;
    background: none;
    border: none;
    color: #ffffff;
    font-size: 1.25rem;
    padding: 0.5rem;
    border-radius: var(--kt-border-radius-sm);
    transition: var(--kt-transition);
}

.header-menu-toggle:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

@media (max-width: 991.98px) {
    .header-menu-toggle {
        display: block;
    }
}

/* 导航菜单居中样式 */
.header-menu.justify-content-center {
    display: flex;
    justify-content: center;
    align-items: center;
}

.header-menu.flex-grow-1 {
    flex-grow: 1;
}

/* 通知和消息图标 */
.header-notifications {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-right: 1rem;
}

.header-notification-btn {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background: none;
    border: none;
    color: #a1a5b7;
    border-radius: var(--kt-border-radius-sm);
    transition: var(--kt-transition);
}

.header-notification-btn:hover {
    color: #ffffff;
    background-color: rgba(255, 255, 255, 0.1);
}

.header-notification-badge {
    position: absolute;
    top: 8px;
    right: 8px;
    width: 8px;
    height: 8px;
    background-color: var(--kt-danger);
    border-radius: 50%;
    border: 2px solid var(--kt-header-bg);
}

@media (max-width: 575.98px) {
    .header-notifications {
        display: none;
    }
}

/* 醒目的登录按钮样式 */
.btn-login-highlight {
    position: relative;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 0.6rem 1.5rem;
    font-size: 0.875rem;
    font-weight: 600;
    text-decoration: none;
    border-radius: 25px;
    border: 2px solid transparent;
    background: linear-gradient(135deg, #009ef7 0%, #4facfe 100%);
    color: #ffffff;
    box-shadow: 0 4px 15px rgba(0, 158, 247, 0.3);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    overflow: hidden;
    white-space: nowrap;
    text-transform: none;
    letter-spacing: 0.5px;
}

.btn-login-highlight::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.6s;
}

.btn-login-highlight:hover {
    color: #ffffff;
    text-decoration: none;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 158, 247, 0.4);
    background: linear-gradient(135deg, #4facfe 0%, #009ef7 100%);
}

.btn-login-highlight:hover::before {
    left: 100%;
}

.btn-login-highlight:active {
    transform: translateY(-1px);
    box-shadow: 0 6px 20px rgba(0, 158, 247, 0.35);
}

.btn-login-highlight i {
    font-size: 0.8rem;
    margin-right: 0.5rem;
    transition: transform 0.3s ease;
}

.btn-login-highlight:hover i {
    transform: translateX(2px);
}

/* 脉冲动画效果 */
.btn-login-highlight::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.3);
    transform: translate(-50%, -50%);
    transition: width 0.6s, height 0.6s;
}

.btn-login-highlight:hover::after {
    width: 300px;
    height: 300px;
}

/* 响应式适配 */
@media (max-width: 768px) {
    .btn-login-highlight {
        padding: 0.5rem 1.2rem;
        font-size: 0.8rem;
        border-radius: 20px;
    }
}

@media (max-width: 575.98px) {
    .btn-login-highlight {
        padding: 0.45rem 1rem;
        font-size: 0.75rem;
        border-radius: 18px;
        box-shadow: 0 3px 12px rgba(0, 158, 247, 0.25);
    }

    .btn-login-highlight:hover {
        box-shadow: 0 6px 20px rgba(0, 158, 247, 0.35);
    }
}
