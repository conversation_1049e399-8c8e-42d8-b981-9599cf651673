/* Keen Theme CSS - 基于 Keen 主题的全站样式 */

:root {
    /* 主色调 - 参照 Keen 主题 */
    --kt-primary: #009ef7;
    --kt-primary-light: #f1faff;
    --kt-primary-inverse: #ffffff;
    --kt-secondary: #e4e6ea;
    --kt-secondary-light: #f8f9fa;
    --kt-secondary-inverse: #3f4254;
    --kt-success: #50cd89;
    --kt-success-light: #e8fff3;
    --kt-success-inverse: #ffffff;
    --kt-info: #7239ea;
    --kt-info-light: #f8f5ff;
    --kt-info-inverse: #ffffff;
    --kt-warning: #ffc700;
    --kt-warning-light: #fff8dd;
    --kt-warning-inverse: #ffffff;
    --kt-danger: #f1416c;
    --kt-danger-light: #fff5f8;
    --kt-danger-inverse: #ffffff;
    --kt-dark: #181c32;
    --kt-dark-light: #eff2f5;
    --kt-dark-inverse: #ffffff;
    --kt-light: #f5f8fa;
    --kt-light-light: #ffffff;
    --kt-light-inverse: #7e8299;

    /* 文本颜色 - 优化视觉体验 */
    --kt-text-dark: #1e293b;
    --kt-text-muted: #64748b;
    --kt-text-gray-400: #94a3b8;
    --kt-text-gray-500: #64748b;
    --kt-text-gray-600: #475569;
    --kt-text-gray-700: #334155;
    --kt-text-gray-800: #1e293b;
    --kt-text-gray-900: #0f172a;

    /* 背景颜色 - 优化视觉体验 */
    --kt-body-bg: #f8fafc;
    --kt-body-color: #334155;
    --kt-card-bg: #ffffff;
    --kt-card-border-color: #e2e8f0;

    /* 边框和阴影 */
    --kt-border-color: #eff2f5;
    --kt-border-dashed-color: #e4e6ea;
    --kt-box-shadow-xs: 0 0.1rem 0.75rem 0.25rem rgba(0, 0, 0, 0.05);
    --kt-box-shadow-sm: 0 0.1rem 1rem 0.25rem rgba(0, 0, 0, 0.05);
    --kt-box-shadow: 0 0.5rem 1.5rem 0.5rem rgba(0, 0, 0, 0.075);
    --kt-box-shadow-lg: 0 1rem 3rem 1rem rgba(0, 0, 0, 0.175);

    /* 圆角 */
    --kt-border-radius: 0.625rem;
    --kt-border-radius-sm: 0.475rem;
    --kt-border-radius-lg: 0.75rem;

    /* 间距 */
    --kt-spacer: 1rem;

    /* 字体 */
    --kt-font-sans-serif: Inter, Helvetica, "sans-serif";
    --kt-font-size-base: 1rem;
    --kt-font-weight-normal: 400;
    --kt-font-weight-semibold: 600;
    --kt-font-weight-bold: 700;
    --kt-font-weight-bolder: 800;

    /* 过渡动画 */
    --kt-transition: all 0.2s ease;
    --kt-transition-fast: all 0.15s ease;
    --kt-transition-slow: all 0.3s ease;

    /* Header 相关 */
    --kt-header-height: 70px;
    --kt-header-bg: #181c32;
    --kt-header-border-color: #2b3445;

    /* Sidebar 相关 */
    --kt-sidebar-width: 265px;
    --kt-sidebar-bg: #ffffff;
    --kt-sidebar-border-color: #eff2f5;

    /* 工具栏 */
    --kt-toolbar-height: 55px;
    --kt-toolbar-bg: #ffffff;
}

/* 全局样式重置 */
* {
    box-sizing: border-box;
}

body {
    font-family: var(--kt-font-sans-serif);
    font-size: var(--kt-font-size-base);
    font-weight: var(--kt-font-weight-normal);
    line-height: 1.5;
    color: var(--kt-body-color);
    background-color: var(--kt-body-bg);
    -webkit-text-size-adjust: 100%;
    -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
}

/* 链接样式 */
a {
    color: var(--kt-primary);
    text-decoration: none;
    transition: var(--kt-transition);
}

a:hover {
    color: var(--kt-primary);
    text-decoration: none;
}

/* 标题样式 - 优化字体颜色和层次 */
h1, h2, h3, h4, h5, h6 {
    margin-top: 0;
    margin-bottom: 0.5rem;
    font-weight: var(--kt-font-weight-semibold);
    line-height: 1.2;
    color: var(--kt-text-dark);
    letter-spacing: -0.025em;
}

h1 {
    font-size: 2.25rem;
    font-weight: var(--kt-font-weight-bold);
    color: var(--kt-text-gray-900);
}
h2 {
    font-size: 1.875rem;
    font-weight: var(--kt-font-weight-bold);
    color: var(--kt-text-gray-800);
}
h3 {
    font-size: 1.5rem;
    color: var(--kt-text-gray-800);
}
h4 {
    font-size: 1.25rem;
    color: var(--kt-text-gray-700);
}
h5 {
    font-size: 1.125rem;
    color: var(--kt-text-gray-700);
}
h6 {
    font-size: 1rem;
    color: var(--kt-text-gray-600);
}

/* 段落样式 - 优化可读性 */
p {
    margin-top: 0;
    margin-bottom: 1rem;
    line-height: 1.6;
    color: var(--kt-text-gray-600);
}

.lead {
    font-size: 1.125rem;
    font-weight: 400;
    line-height: 1.6;
    color: var(--kt-text-gray-600);
}

.text-muted {
    color: var(--kt-text-muted) !important;
}

.small, small {
    font-size: 0.875rem;
    color: var(--kt-text-gray-500);
}

/* 按钮基础样式 */
.btn {
    display: inline-block;
    font-weight: var(--kt-font-weight-semibold);
    line-height: 1.5;
    color: var(--kt-body-color);
    text-align: center;
    text-decoration: none;
    vertical-align: middle;
    cursor: pointer;
    user-select: none;
    background-color: transparent;
    border: 1px solid transparent;
    padding: 0.75rem 1.5rem;
    font-size: var(--kt-font-size-base);
    border-radius: var(--kt-border-radius);
    transition: var(--kt-transition);
}

.btn:hover {
    text-decoration: none;
}

.btn:focus {
    outline: 0;
    box-shadow: 0 0 0 0.2rem rgba(0, 158, 247, 0.25);
}

/* 主要按钮样式 - 优化视觉效果 */
.btn-primary {
    color: var(--kt-primary-inverse);
    background: linear-gradient(135deg, var(--kt-primary) 0%, #0085d1 100%);
    border: none;
    box-shadow: 0 2px 8px rgba(0, 158, 247, 0.2);
    font-weight: var(--kt-font-weight-semibold);
}

.btn-primary:hover {
    color: var(--kt-primary-inverse);
    background: linear-gradient(135deg, #0085d1 0%, #007bb5 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 158, 247, 0.3);
}

.btn-primary:active {
    transform: translateY(0);
    box-shadow: 0 2px 6px rgba(0, 158, 247, 0.25);
}

/* 小尺寸按钮 */
.btn-sm {
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
    border-radius: var(--kt-border-radius-sm);
    font-weight: var(--kt-font-weight-semibold);
}

/* 按钮悬停效果增强 */
.btn {
    position: relative;
    overflow: hidden;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.btn:hover::before {
    left: 100%;
}

/* 卡片样式 */
.card {
    position: relative;
    display: flex;
    flex-direction: column;
    min-width: 0;
    word-wrap: break-word;
    background-color: var(--kt-card-bg);
    background-clip: border-box;
    border: 1px solid var(--kt-card-border-color);
    border-radius: var(--kt-border-radius);
    box-shadow: var(--kt-box-shadow-xs);
    transition: var(--kt-transition);
}

.card:hover {
    box-shadow: var(--kt-box-shadow-sm);
}

.card-header {
    padding: 1.5rem 1.5rem ;
    margin-bottom: 0;
    background-color: transparent;
    border-bottom: 0;
}

.card-body {
    flex: 1 1 auto;
    padding: 1.5rem;
}

.card-footer {
    padding: 0 1.5rem 1.5rem;
    background-color: transparent;
    border-top: 0;
}

/* 表单样式 */
.form-control {
    display: block;
    width: 100%;
    padding: 0.75rem 1rem;
    font-size: var(--kt-font-size-base);
    font-weight: var(--kt-font-weight-normal);
    line-height: 1.5;
    color: var(--kt-text-gray-700);
    background-color: var(--kt-card-bg);
    background-image: none;
    border: 1px solid var(--kt-border-color);
    border-radius: var(--kt-border-radius);
    box-shadow: none;
    transition: var(--kt-transition);
}

.form-control:focus {
    color: var(--kt-text-gray-700);
    background-color: var(--kt-card-bg);
    border-color: var(--kt-primary);
    outline: 0;
    box-shadow: 0 0 0 0.2rem rgba(0, 158, 247, 0.25);
}

/* 导航栏样式 */
.navbar {
    position: relative;
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    justify-content: space-between;
    padding: 0.5rem 1rem;
    transition: var(--kt-transition);
}

.navbar-brand {
    display: inline-block;
    padding-top: 0.3125rem;
    padding-bottom: 0.3125rem;
    margin-right: 1rem;
    font-size: 1.25rem;
    font-weight: var(--kt-font-weight-bold);
    line-height: inherit;
    text-decoration: none;
    white-space: nowrap;
}

/* 响应式工具类 */
.d-none { display: none !important; }
.d-inline { display: inline !important; }
.d-inline-block { display: inline-block !important; }
.d-block { display: block !important; }
.d-flex { display: flex !important; }

/* 文本工具类 */
.text-center { text-align: center !important; }
.text-left { text-align: left !important; }
.text-right { text-align: right !important; }

.text-primary { color: var(--kt-primary) !important; }
.text-secondary { color: var(--kt-secondary) !important; }
.text-success { color: var(--kt-success) !important; }
.text-danger { color: var(--kt-danger) !important; }
.text-warning { color: var(--kt-warning) !important; }
.text-info { color: var(--kt-info) !important; }
.text-dark { color: var(--kt-dark) !important; }
.text-muted { color: var(--kt-text-muted) !important; }

/* 背景工具类 */
.bg-primary { background-color: var(--kt-primary) !important; }
.bg-secondary { background-color: var(--kt-secondary) !important; }
.bg-success { background-color: var(--kt-success) !important; }
.bg-danger { background-color: var(--kt-danger) !important; }
.bg-warning { background-color: var(--kt-warning) !important; }
.bg-info { background-color: var(--kt-info) !important; }
.bg-dark { background-color: var(--kt-dark) !important; }
.bg-light { background-color: var(--kt-light) !important; }

/* 间距工具类 */
.m-0 { margin: 0 !important; }
.mt-0 { margin-top: 0 !important; }
.mr-0 { margin-right: 0 !important; }
.mb-0 { margin-bottom: 0 !important; }
.ml-0 { margin-left: 0 !important; }

.p-0 { padding: 0 !important; }
.pt-0 { padding-top: 0 !important; }
.pr-0 { padding-right: 0 !important; }
.pb-0 { padding-bottom: 0 !important; }
.pl-0 { padding-left: 0 !important; }

/* 阴影工具类 */
.shadow-xs { box-shadow: var(--kt-box-shadow-xs) !important; }
.shadow-sm { box-shadow: var(--kt-box-shadow-sm) !important; }
.shadow { box-shadow: var(--kt-box-shadow) !important; }
.shadow-lg { box-shadow: var(--kt-box-shadow-lg) !important; }
.shadow-none { box-shadow: none !important; }

/* 圆角工具类 */
.rounded { border-radius: var(--kt-border-radius) !important; }
.rounded-sm { border-radius: var(--kt-border-radius-sm) !important; }
.rounded-lg { border-radius: var(--kt-border-radius-lg) !important; }
.rounded-0 { border-radius: 0 !important; }

/* Hover 效果工具类 */
.hover-primary:hover {
    color: var(--kt-primary) !important;
    transition: var(--kt-transition);
}

.hover-lift:hover {
    transform: translateY(-2px);
    transition: var(--kt-transition);
}

.hover-shadow:hover {
    box-shadow: var(--kt-box-shadow-sm) !important;
    transition: var(--kt-transition);
}

/* 页面特定样式 */
.hero-section {
    background: linear-gradient(135deg, var(--kt-primary) 0%, var(--kt-info) 100%);
    color: white;
    padding: 100px 0;
    position: relative;
    overflow: hidden;
}

.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.05)"/><circle cx="10" cy="60" r="0.5" fill="rgba(255,255,255,0.05)"/><circle cx="90" cy="40" r="0.5" fill="rgba(255,255,255,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.3;
}

.feature-card {
    transition: var(--kt-transition-slow);
    border: 1px solid var(--kt-border-color);
    border-radius: var(--kt-border-radius);
    overflow: hidden;
    height: 100%;
    box-shadow: var(--kt-box-shadow-xs);
    background: var(--kt-card-bg);
}

.feature-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--kt-box-shadow-sm);
    border-color: var(--kt-primary);
}

/* 统计卡片样式 */
.stats-card {
    background: var(--kt-card-bg);
    border: 1px solid var(--kt-border-color);
    border-radius: var(--kt-border-radius);
    padding: 2rem;
    text-align: center;
    transition: var(--kt-transition);
    position: relative;
    overflow: hidden;
}

.stats-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--kt-primary), var(--kt-info));
    transform: scaleX(0);
    transition: var(--kt-transition);
}

.stats-card:hover::before {
    transform: scaleX(1);
}

.stats-card:hover {
    transform: translateY(-3px);
    box-shadow: var(--kt-box-shadow-sm);
}

.stats-number {
    font-size: 2.5rem;
    font-weight: var(--kt-font-weight-bold);
    color: var(--kt-primary);
    margin-bottom: 0.5rem;
}

.stats-label {
    color: var(--kt-text-muted);
    font-weight: var(--kt-font-weight-semibold);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-size: 0.875rem;
}

/* 登录页面特殊样式 */
.login-bg {
    background: linear-gradient(135deg, var(--kt-primary) 0%, var(--kt-info) 100%);
    position: relative;
    overflow: hidden;
}

.login-bg::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="dots" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23dots)"/></svg>');
    opacity: 0.3;
}

.login-card {
    backdrop-filter: blur(10px);
    background: rgba(255, 255, 255, 0.95);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

/* 表单增强样式 */
.form-control-lg {
    padding: 0.875rem 1rem;
    font-size: 1rem;
    border-radius: var(--kt-border-radius);
    border: 2px solid var(--kt-border-color);
    transition: var(--kt-transition);
}

.form-control-lg:focus {
    border-color: var(--kt-primary);
    box-shadow: 0 0 0 0.2rem rgba(0, 158, 247, 0.15);
    background-color: #ffffff;
}

.form-control-lg.ps-5 {
    padding-left: 3rem;
}

/* 按钮增强样式 */
.btn-lg {
    padding: 0.875rem 2rem;
    font-size: 1rem;
    font-weight: var(--kt-font-weight-semibold);
    border-radius: var(--kt-border-radius);
    transition: var(--kt-transition);
}

.btn-primary.btn-lg {
    background: linear-gradient(135deg, var(--kt-primary) 0%, #0085d1 100%);
    border: none;
    box-shadow: 0 4px 12px rgba(0, 158, 247, 0.3);
}

.btn-primary.btn-lg:hover {
    background: linear-gradient(135deg, #0085d1 0%, #007bb5 100%);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 158, 247, 0.4);
}

.btn-outline-primary.btn-lg {
    border: 2px solid var(--kt-primary);
    color: var(--kt-primary);
    background: transparent;
}

.btn-outline-primary.btn-lg:hover {
    background: var(--kt-primary);
    border-color: var(--kt-primary);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 158, 247, 0.3);
}

/* 输入组样式 */
.input-group .form-control-lg {
    border-right: none;
}

.input-group .btn {
    border-left: none;
    border: 2px solid var(--kt-border-color);
    border-left: none;
}

.input-group:focus-within .form-control-lg,
.input-group:focus-within .btn {
    border-color: var(--kt-primary);
}

/* 响应式调整 */
@media (max-width: 767.98px) {
    .hero-section {
        padding: 60px 0;
    }

    .hero-section .display-4 {
        font-size: 2rem;
    }

    .stats-card {
        padding: 1.5rem;
    }

    .stats-number {
        font-size: 2rem;
    }

    .feature-card .card-body {
        padding: 2rem 1.5rem;
    }

    .login-card .card-body {
        padding: 2rem 1.5rem;
    }
}

@media (max-width: 575.98px) {
    .hero-section {
        padding: 40px 0;
    }

    .hero-section .btn {
        display: block;
        width: 100%;
        margin-bottom: 0.5rem;
    }

    .hero-section .d-flex {
        flex-direction: column;
    }

    .stats-card {
        padding: 1rem;
        margin-bottom: 1rem;
    }

    .feature-card .rounded-circle {
        width: 60px !important;
        height: 60px !important;
    }

    .feature-card .fa-2x {
        font-size: 1.5rem !important;
    }
}

/* 动画效果 */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(0, 158, 247, 0.4);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(0, 158, 247, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(0, 158, 247, 0);
    }
}

.pulse {
    animation: pulse 2s infinite;
}

/* 工具提示样式 */
.tooltip {
    font-size: 0.875rem;
}

.tooltip-inner {
    background-color: var(--kt-dark);
    color: #ffffff;
    border-radius: var(--kt-border-radius-sm);
    padding: 0.5rem 0.75rem;
}

/* 加载状态 */
.loading {
    position: relative;
    pointer-events: none;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid transparent;
    border-top: 2px solid #ffffff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 页面整体优化 */
body {
    font-family: var(--kt-font-sans-serif);
    font-size: var(--kt-font-size-base);
    font-weight: var(--kt-font-weight-normal);
    line-height: 1.6;
    color: var(--kt-body-color);
    background-color: var(--kt-body-bg);
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* 链接优化 */
a {
    color: var(--kt-primary);
    text-decoration: none;
    transition: var(--kt-transition);
}

a:hover {
    color: #0085d1;
    text-decoration: none;
}

/* 卡片内容优化 */
.card-title {
    color: var(--kt-text-gray-800);
    font-weight: var(--kt-font-weight-semibold);
    margin-bottom: 0.75rem;
}

.card-text {
    color: var(--kt-text-gray-600);
    line-height: 1.6;
}

/* 表单标签优化 */
.form-label {
    color: var(--kt-text-gray-700);
    font-weight: var(--kt-font-weight-semibold);
    margin-bottom: 0.5rem;
}

/* 导航链接优化 */
.nav-link {
    color: var(--kt-text-gray-600);
    font-weight: var(--kt-font-weight-normal);
    transition: var(--kt-transition);
}

.nav-link:hover {
    color: var(--kt-primary);
}

.nav-link.active {
    color: var(--kt-primary);
    font-weight: var(--kt-font-weight-semibold);
}

/* 下拉菜单优化 */
.dropdown-item {
    color: var(--kt-text-gray-700);
    font-weight: var(--kt-font-weight-normal);
    transition: var(--kt-transition);
}

.dropdown-item:hover {
    color: var(--kt-text-gray-800);
    background-color: var(--kt-light);
}

/* 面包屑导航优化 */
.breadcrumb-item {
    color: var(--kt-text-gray-500);
}

.breadcrumb-item.active {
    color: var(--kt-text-gray-700);
    font-weight: var(--kt-font-weight-semibold);
}

/* 徽章优化 */
.badge {
    font-weight: var(--kt-font-weight-semibold);
    letter-spacing: 0.025em;
}

/* 警告框优化 */
.alert {
    border: none;
    border-radius: var(--kt-border-radius);
    font-weight: var(--kt-font-weight-normal);
}

.alert-primary {
    background-color: var(--kt-primary-light);
    color: var(--kt-primary);
}

.alert-success {
    background-color: var(--kt-success-light);
    color: var(--kt-success);
}

.alert-warning {
    background-color: var(--kt-warning-light);
    color: var(--kt-warning);
}

.alert-danger {
    background-color: var(--kt-danger-light);
    color: var(--kt-danger);
}
