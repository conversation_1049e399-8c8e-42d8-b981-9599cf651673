# 智能体矩阵 公开API接口文档

## 🚀 项目信息
- **项目名称**: 智能体矩阵 智能文档识别与解析平台
- **版本**: v1.0.0
- **基础URL**: `https://api.sinoair-agent.com/api/v1/public`
- **文档更新**: 2024-12-04

## 🔐 认证方式
- **类型**: API Key
- **Header**: `X-API-Key: {your_api_key}`
- **格式**: `keyId.keySecret`
- **API Key获取**: 通过客户门户网站申请获取
- **有效期**: 根据申请时设置的过期时间

### 🎯 API Key申请流程
1. 访问客户门户网站：`https://portal.sinoair-agent.com`
2. 注册账号并完成邮箱验证
3. 登录后进入控制台，点击"创建API Key"
4. 填写API Key名称和描述，设置过期时间（可选）
5. 创建成功后，请立即复制并保存完整的API Key
6. 在API请求中使用该API Key进行认证

### 🔑 认证方式说明
支持以下三种方式传递API Key：

1. **HTTP Header（推荐）**：
   ```
   X-API-Key: ak_1234567890abcdef.abcdef1234567890abcdef1234567890abcdef12
   ```

2. **Authorization Header**：
   ```
   Authorization: Bearer ak_1234567890abcdef.abcdef1234567890abcdef1234567890abcdef12
   ```

3. **查询参数**：
   ```
   https://api.sinoair-agent.com/api/v1/public/agents?api_key=ak_1234567890abcdef.abcdef1234567890abcdef1234567890abcdef12
   ```

---

## 📋 1. Agent列表查询接口

### 接口概述
获取所有已发布的Agent列表，用于获取AgentCode供文件解析接口使用。只返回状态为"已发布"的Agent。

### 请求信息
- **HTTP方法**: `GET`
- **请求URL**: `/agents`
- **Content-Type**: `application/json`

### 请求参数
#### Query Parameters
| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| page | int | 否 | 1 | 页码，从1开始 |
| size | int | 否 | 10 | 每页数量，最大100 |
| agentId | string | 否 | - | 指定Agent Code，用于查询特定Agent信息 |

### 响应信息
#### 成功响应 (200 OK)
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "total": 25,
    "page": 1,
    "size": 10,
    "agents": [
      {
        "agentCode": "pdf_parser_v1",
        "name": "PDF文档解析Agent",
        "description": "专门用于解析PDF文档内容，提取文本、表格和图片信息",
        "supportedFileTypes": ["pdf", "jpg", "jpeg", "png", "gif"],
        "maxFileSize": "5MB"
      },
      {
        "agentCode": "image_ocr_v2",
        "name": "图片OCR识别Agent",
        "description": "用于识别和解析图片中的文字和内容，支持多种语言",
        "supportedFileTypes": ["pdf", "jpg", "jpeg", "png", "gif"],
        "maxFileSize": "5MB"
      }
    ]
  },
  "timestamp": "2024-12-04 15:30:00"
}
```

---

## 📄 2. 文件解析接口

### 接口概述
根据指定的AgentCode上传单个文件，调用对应的Agent解析文件内容并返回JSON格式的结构化数据。

**⚠️ 重要说明**：
- 🔄 **当前仅支持同步处理模式**
- ⏱️ **建议文件大小 < 5MB**，避免请求超时
- 📁 **一次只能上传一个文件**进行解析
- 🎯 **只能调用已发布状态的Agent**

### 请求信息
- **HTTP方法**: `POST`
- **请求URL**: `/files/parse`
- **Content-Type**: `multipart/form-data`

### 请求参数
#### Form Data Parameters
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| agentCode | string | 是 | Agent标识符，通过Agent列表接口获取 |
| file | file | 是 | 上传的文件 |

#### 文件限制
- **支持格式**: PDF (.pdf), 图片 (.jpg, .jpeg, .png, .gif)
- **文件大小**: 最大5MB（所有格式统一限制）
- **文件名**: 支持中英文，不超过255个字符
- **上传限制**: 一次只能上传一个文件，不支持批量上传
- **Agent状态**: 只能调用已发布状态的Agent



### 响应信息

#### 成功响应 (200 OK)
```json
{
  "code": 200,
  "message": "文件解析成功",
  "data": {
    "taskId": "task_1733299800000_a1b2c3d4",
    "agentId": "pdf_parser_v1",
    "agentName": "PDF文档解析Agent",
    "fileName": "document.pdf",
    "fileSize": "2.5MB",
    "parsedContent": {
      "title": "项目需求文档",
      "author": "张三",
      "createdDate": "2024-01-20",
      "content": [
        {
          "type": "text",
          "page": 1,
          "content": "这是文档的主要内容..."
        },
        {
          "type": "table",
          "page": 2,
          "headers": ["项目名称", "负责人", "截止日期"],
          "rows": [
            ["项目A", "李四", "2024-02-01"],
            ["项目B", "王五", "2024-02-15"]
          ]
        }
      ],
      "metadata": {
        "totalPages": 5,
        "wordCount": 1250,
        "language": "zh-cn"
      },
      "agentCode": "pdf_parser_v1",
      "agentName": "PDF文档解析Agent",
      "processedAt": "2024-12-04 15:30:00"
    },
    "processingTime": "3.2s",
    "timestamp": "2024-12-04 15:30:00"
  }
}
```

#### 错误响应示例

**文件格式不支持 (400)**
```json
{
  "code": 10004,
  "message": "不支持的文件格式，仅支持PDF和图片文件",
  "timestamp": "2024-12-04 15:30:00"
}
```

**Agent不存在或未发布 (400)**
```json
{
  "code": 10003,
  "message": "Agent不存在或未发布",
  "timestamp": "2024-12-04 15:30:00"
}
```

**文件大小超限 (413)**
```json
{
  "code": 10005,
  "message": "文件大小超出限制，最大支持5MB",
  "timestamp": "2024-12-04 15:30:00"
}
```

**API Key认证失败 (401)**
```json
{
  "code": 10001,
  "message": "Missing API Key authentication. Please provide X-API-Key header with format: keyId.keySecret",
  "timestamp": "2024-12-04 15:30:00"
}
```

---

## 📊 状态码说明
| HTTP状态码 | 说明 |
|------------|------|
| 200 | 请求成功 |
| 400 | 请求参数错误或文件格式不支持 |
| 401 | 未授权访问或API Key无效 |
| 413 | 文件大小超出限制 |
| 422 | 文件内容无法解析 |
| 500 | 服务器内部错误 |

---

## 🔢 错误码对照表

| 错误码 | HTTP状态码 | 说明 |
|--------|------------|------|
| 200 | 200 | 操作成功 |
| 10001 | 400 | 缺少API Key认证或参数错误 |
| 10003 | 400 | Agent不存在或未发布 |
| 10004 | 400 | 文件格式不支持 |
| 10005 | 413 | 文件大小超出限制 |
| 30001 | 500 | 文件解析服务异常 |

---

## 💻 使用示例

### 1. 获取Agent列表
```bash
curl -X GET "https://api.sinoair-agent.com/api/v1/public/agents" \
  -H "X-API-Key: ak_1234567890abcdef.abcdef1234567890abcdef1234567890abcdef12" \
  -H "Content-Type: application/json"
```

### 2. 文件解析（同步模式）
```bash
curl -X POST "https://api.sinoair-agent.com/api/v1/public/files/parse" \
  -H "X-API-Key: ak_1234567890abcdef.abcdef1234567890abcdef1234567890abcdef12" \
  -F "agentCode=pdf_parser_v1" \
  -F "file=@/path/to/document.pdf"
```

### 3. 解析图片文件
```bash
curl -X POST "https://api.sinoair-agent.com/api/v1/public/files/parse" \
  -H "X-API-Key: ak_1234567890abcdef.abcdef1234567890abcdef1234567890abcdef12" \
  -F "agentCode=image_ocr_v2" \
  -F "file=@/path/to/image.jpg"
```

### 4. 使用Authorization Header方式
```bash
curl -X GET "https://api.sinoair-agent.com/api/v1/public/agents" \
  -H "Authorization: Bearer ak_1234567890abcdef.abcdef1234567890abcdef1234567890abcdef12" \
  -H "Content-Type: application/json"
```

### 5. 使用查询参数方式
```bash
curl -X GET "https://api.sinoair-agent.com/api/v1/public/agents?api_key=ak_1234567890abcdef.abcdef1234567890abcdef1234567890abcdef12"
```

### 6. 查询特定Agent信息
```bash
curl -X GET "https://api.sinoair-agent.com/api/v1/public/agents?agentId=pdf_parser_v1" \
  -H "X-API-Key: ak_1234567890abcdef.abcdef1234567890abcdef1234567890abcdef12"
```

---

## ⚠️ 注意事项

1. **API Key申请**: 请访问客户门户网站 `https://portal.sinoair-agent.com` 注册账号并申请API Key
2. **API Key安全**: 请妥善保管您的API Key，不要在客户端代码中硬编码或公开分享
3. **文件大小限制**: 请确保上传的文件不超过5MB
4. **处理模式**:
   - ⚡ **当前仅支持同步模式**：适用于小文件（<5MB）的实时处理
   - 🔄 **异步模式**: 暂时不可用，后续版本将支持
5. **单文件处理**: 每次请求只能上传一个文件，如需处理多个文件请分别调用
6. **Agent状态**: 只能调用已发布状态的Agent，草稿或测试中的Agent无法通过公开API访问
7. **请求限制**: 每个API Key有不同的请求频率限制，请查看您的配额设置
8. **重试机制**: 如果解析失败，系统会自动重试最多3次
9. **IP限制**: 如果设置了IP白名单，请确保从允许的IP地址发起请求
10. **过期时间**: 请注意API Key的过期时间，过期后需要重新创建

---

## 📞 联系方式

如有问题或需要技术支持，请联系：
- **客户门户**: https://portal.sinoair-agent.com
- **技术支持邮箱**: <EMAIL>
- **销售咨询**: <EMAIL>
- **文档更新**: 请关注本文档的最新版本

## 🚀 快速开始

1. **注册账号**: 访问 https://portal.sinoair-agent.com 注册账号
2. **创建API Key**: 登录后在控制台创建您的第一个API Key
3. **测试接口**: 使用API Key调用Agent列表接口进行测试
4. **集成开发**: 参考本文档进行API集成开发

## 📝 版本更新

### v1.0.0 (2024-12-04)
- 初始版本发布
- 支持API Key认证方式（三种认证方式）
- 提供Agent列表查询和文件解析功能
- 当前仅支持同步处理模式
- 支持PDF和图片文件格式
- 最大文件大小限制为5MB
- 只能调用已发布状态的Agent
