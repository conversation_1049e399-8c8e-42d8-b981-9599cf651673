import { defineConfig, loadEnv } from 'vite'
import vue from '@vitejs/plugin-vue'
import { resolve } from 'path'

// https://vitejs.dev/config/
export default defineConfig(({ command, mode }) => {
  // 加载环境变量
  const env = loadEnv(mode, process.cwd(), '')

  return {
  plugins: [vue()],
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src'),
    },
  },
  server: {
    port: parseInt(env.VITE_DEV_PORT) || 8001,
    host: env.VITE_DEV_HOST || 'localhost',
    proxy: env.VITE_PROXY_ENABLED === 'true' ? {
      '/api': {
        target: env.VITE_PROXY_TARGET || 'http://localhost:8088',
        changeOrigin: true,
        secure: false,
        // 不需要rewrite，保持/api路径
        configure: (proxy, options) => {
          console.log(`🔗 API代理配置: ${options.target}`)
        }
      }
      // 移除 /auth 代理，让Vue前端处理 /auth/login 路由
      // 后端的认证API都在 /api/auth 下，已经被上面的 /api 代理覆盖
      // 所有Vue前端API都统一使用 /api/** 路径
    } : {}
  },
  build: {
    outDir: 'dist',
    assetsDir: 'assets',
    sourcemap: env.VITE_BUILD_SOURCEMAP === 'true',
    minify: env.VITE_BUILD_MINIFY !== 'false',
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['vue', 'vue-router', 'pinia'],
          ui: ['bootstrap', '@fortawesome/fontawesome-free', 'sweetalert2']
        }
      }
    }
  },

  // 环境变量配置
  define: {
    __APP_ENV__: JSON.stringify(env.VITE_APP_ENV),
    __APP_TITLE__: JSON.stringify(env.VITE_APP_TITLE),
    __API_BASE_URL__: JSON.stringify(env.VITE_API_BASE_URL),
    __DEBUG__: JSON.stringify(env.VITE_DEBUG === 'true')
  }

  }
})
