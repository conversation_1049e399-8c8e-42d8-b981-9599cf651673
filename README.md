# 智能体矩阵 - Vue.js 前端

基于 Vue 3 + Vite 构建的智能体矩阵前端应用，提供完整的用户界面和交互体验。

## 技术栈

- **Vue 3** - 渐进式 JavaScript 框架
- **Vite** - 下一代前端构建工具
- **Vue Router 4** - 官方路由管理器
- **Pinia** - Vue 状态管理库
- **Bootstrap 5** - CSS 框架
- **Axios** - HTTP 客户端
- **Font Awesome** - 图标库
- **SweetAlert2** - 美观的弹窗组件
- **Vue Toastification** - Toast 通知组件

## 功能特性

### 🏠 首页
- 产品介绍和特性展示
- 动画效果和交互体验
- 响应式设计

### 🔐 用户认证
- 邮箱验证码登录
- 自动用户创建
- JWT 令牌管理
- 路由守卫保护

### 📊 仪表盘
- 用户统计数据展示
- 订阅Agent管理
- 最近调用历史
- 快速操作入口

### 🤖 Agent广场
- Agent列表展示
- 搜索和分类筛选
- 订阅/取消订阅功能
- 分页浏览

### 📄 Agent详情
- 详细信息展示
- 使用示例和文档
- 相关推荐
- 订阅状态管理

### 📈 调用历史
- 历史记录查询
- 状态筛选
- 表格/列表视图切换
- 分页和搜索

### 🔑 API密钥管理
- 密钥创建和删除
- 状态管理
- 使用统计
- 安全提示

### 📚 其他页面
- 产品介绍
- 价格方案
- API文档
- 帮助中心

## 项目结构

```
front/
├── public/                 # 静态资源
├── src/
│   ├── components/         # 通用组件
│   │   ├── Header.vue     # 头部导航
│   │   ├── Footer.vue     # 底部信息
│   │   ├── Loading.vue    # 加载组件
│   │   └── Pagination.vue # 分页组件
│   ├── views/             # 页面组件
│   │   ├── Home.vue       # 首页
│   │   ├── Product.vue    # 产品介绍
│   │   ├── Pricing.vue    # 价格方案
│   │   ├── ApiDocs.vue    # API文档
│   │   ├── auth/          # 认证相关
│   │   ├── dashboard/     # 仪表盘相关
│   │   └── agent/         # Agent相关
│   ├── stores/            # Pinia 状态管理
│   │   ├── auth.js        # 认证状态
│   │   └── agent.js       # Agent状态
│   ├── router/            # 路由配置
│   ├── utils/             # 工具函数
│   │   ├── api.js         # API请求封装
│   │   └── helpers.js     # 辅助函数
│   ├── styles/            # 样式文件
│   └── main.js            # 应用入口
├── package.json           # 依赖配置
├── vite.config.js         # Vite配置
└── README.md              # 项目说明
```

## 开发环境设置

### 环境要求

- Node.js >= 16.0.0
- npm >= 7.0.0 或 yarn >= 1.22.0

### 安装依赖

```bash
# 使用 npm
npm install

# 或使用 yarn
yarn install
```

### 开发服务器

```bash
# 启动开发服务器
npm run dev

# 或
yarn dev
```

应用将在 http://localhost:3000 启动

### 构建生产版本

```bash
# 构建生产版本
npm run build

# 或
yarn build
```

### 预览生产版本

```bash
# 预览构建结果
npm run preview

# 或
yarn preview
```

## 配置说明

### API 代理配置

开发环境下，API请求会代理到后端服务器：

```javascript
// vite.config.js
server: {
  proxy: {
    '/api': {
      target: 'http://localhost:9091',
      changeOrigin: true,
      rewrite: (path) => path.replace(/^\/api/, '')
    }
  }
}
```

### 环境变量

创建 `.env.local` 文件配置环境变量：

```bash
# API 基础URL（可选，默认使用代理）
VITE_API_BASE_URL=http://localhost:9091

# 应用标题
VITE_APP_TITLE=智能体矩阵
```

## 主要功能说明

### 状态管理

使用 Pinia 进行状态管理：

- `useAuthStore` - 用户认证状态
- `useAgentStore` - Agent相关状态

### 路由守卫

实现了完整的路由守卫机制：

- 认证检查
- 权限验证
- 页面标题设置
- 登录重定向

### API 请求

统一的 API 请求处理：

- 请求/响应拦截器
- 错误处理
- 认证令牌管理
- Toast 通知集成

### 响应式设计

完全响应式设计，支持：

- 桌面端 (≥1200px)
- 平板端 (768px-1199px)
- 移动端 (<768px)

## 开发规范

### 代码风格

- 使用 ESLint 进行代码检查
- 遵循 Vue 3 Composition API 规范
- 组件命名使用 PascalCase
- 文件命名使用 kebab-case

### 组件开发

- 优先使用 Composition API
- 合理使用 `<script setup>` 语法
- 组件职责单一
- 适当的注释和文档

### 样式规范

- 使用 SCSS 预处理器
- CSS 变量统一管理
- BEM 命名规范
- 响应式优先

## 部署说明

### 构建优化

生产构建包含以下优化：

- 代码分割
- 资源压缩
- Tree Shaking
- 缓存优化

### 部署步骤

1. 构建生产版本：`npm run build`
2. 将 `dist` 目录部署到 Web 服务器
3. 配置服务器路由重定向到 `index.html`
4. 配置 API 代理或 CORS

### Nginx 配置示例

```nginx
server {
    listen 80;
    server_name your-domain.com;
    root /path/to/dist;
    index index.html;

    # 处理 Vue Router 的 history 模式
    location / {
        try_files $uri $uri/ /index.html;
    }

    # API 代理
    location /api/ {
        proxy_pass http://localhost:9091/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

## 常见问题

### Q: 开发服务器启动失败？
A: 检查 Node.js 版本是否 >= 16.0.0，确保端口 3000 未被占用。

### Q: API 请求失败？
A: 确认后端服务器运行在 9091 端口，检查代理配置是否正确。

### Q: 构建后页面空白？
A: 检查路由配置，确保使用了正确的 base URL。

### Q: 样式不生效？
A: 确认 SCSS 文件正确导入，检查 CSS 变量定义。

## 贡献指南

1. Fork 项目
2. 创建功能分支：`git checkout -b feature/new-feature`
3. 提交更改：`git commit -am 'Add new feature'`
4. 推送分支：`git push origin feature/new-feature`
5. 提交 Pull Request

## 许可证

本项目采用 MIT 许可证。详见 [LICENSE](LICENSE) 文件。

## 联系方式

如有问题或建议，请联系：

- 邮箱：<EMAIL>
- 项目地址：https://github.com/sinoair/agent-portal-vue

---

感谢使用智能体矩阵！🚀
