# Vue前端认证API接口文档

## 概述

本文档描述了智能体矩阵平台Vue前端专用的认证API接口，包括图形验证码生成、邮箱验证码发送和用户登录功能。

**基础URL：** `/api/v1/vue-auth`

**版本：** v1.0.0

**更新时间：** 2024-01-01

## 接口列表

| 接口名称 | 请求方式 | 接口地址 | 描述 |
|---------|---------|----------|------|
| 生成图形验证码 | GET | `/captcha` | 生成图形验证码用于人机验证 |
| 发送邮箱验证码 | POST | `/send-code` | 发送邮箱验证码（需图形验证码） |
| 用户登录 | POST | `/login` | 用户登录验证 |
| 获取用户信息 | GET | `/user-info` | 获取当前登录用户信息 |

## 1. 生成图形验证码

### 接口信息
- **接口地址：** `GET /api/v1/vue-auth/captcha`
- **接口描述：** 生成图形验证码，用于发送邮箱验证码前的人机验证
- **请求方式：** GET
- **是否需要认证：** 否

### 请求参数
无

### 响应格式

#### 成功响应
```json
{
  "success": true,
  "message": "图形验证码生成成功",
  "code": 200,
  "data": {
    "captchaId": "a1b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6",
    "captchaImage": "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQ..."
  }
}
```

#### 响应参数说明
| 参数名 | 类型 | 描述 |
|--------|------|------|
| success | boolean | 请求是否成功 |
| message | string | 响应消息 |
| code | integer | 响应状态码 |
| data.captchaId | string | 验证码唯一标识，32位字符串 |
| data.captchaImage | string | Base64编码的验证码图片 |

#### 错误响应
```json
{
  "success": false,
  "message": "生成图形验证码失败，请稍后重试",
  "code": 500
}
```

### 使用说明
- 验证码图片为110x40像素的JPEG格式
- 验证码包含4位数字和大写字母组合
- 验证码有效期为5分钟
- 验证码为一次性使用，验证后自动失效

## 2. 发送邮箱验证码

### 接口信息
- **接口地址：** `POST /api/v1/vue-auth/send-code`
- **接口描述：** 发送邮箱验证码，需要先通过图形验证码验证
- **请求方式：** POST
- **是否需要认证：** 否

### 请求头
```
Content-Type: application/json
```

### 请求参数
```json
{
  "email": "<EMAIL>",
  "captchaId": "a1b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6",
  "captchaCode": "ABCD"
}
```

#### 请求参数说明
| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| email | string | 是 | 邮箱地址 |
| captchaId | string | 是 | 图形验证码ID |
| captchaCode | string | 是 | 用户输入的图形验证码（4位） |

### 响应格式

#### 成功响应
```json
{
  "success": true,
  "message": "验证码发送成功",
  "code": 200
}
```

#### 错误响应示例

**1. 参数缺失（400）**
```json
{
  "success": false,
  "message": "邮箱不能为空",
  "code": 400
}
```

**2. 图形验证码错误（401）**
```json
{
  "success": false,
  "message": "图形验证码错误或已过期",
  "code": 401
}
```

**3. 发送频率限制（429）**
```json
{
  "success": false,
  "message": "发送过于频繁，请稍后再试",
  "code": 429
}
```

**4. 审核状态异常（403）**
```json
{
  "success": false,
  "message": "您的账户正在审核中，请耐心等待",
  "code": 403
}
```

**5. 系统异常（500）**
```json
{
  "success": false,
  "message": "系统异常，请稍后重试",
  "code": 500
}
```

### 使用说明
- 发送频率限制：同一邮箱60秒内只能发送一次
- 邮箱验证码为6位数字
- 邮箱验证码有效期为5分钟
- 用户审核状态必须为"已通过"才能发送验证码

## 3. 用户登录

### 接口信息
- **接口地址：** `POST /api/v1/vue-auth/login`
- **接口描述：** 用户登录，验证邮箱验证码并返回JWT Token
- **请求方式：** POST
- **是否需要认证：** 否

### 请求头
```
Content-Type: application/json
```

### 请求参数
```json
{
  "email": "<EMAIL>",
  "verificationCode": "123456"
}
```

#### 请求参数说明
| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| email | string | 是 | 邮箱地址 |
| verificationCode | string | 是 | 邮箱验证码（6位数字） |

### 响应格式

#### 成功响应
```json
{
  "success": true,
  "message": "登录成功",
  "code": 200,
  "data": {
    "user": {
      "id": 1,
      "email": "<EMAIL>",
      "username": "user",
      "realName": "张三",
      "lastLoginTime": "2024-01-01 12:00:00",
      "createdTime": "2024-01-01 10:00:00"
    },
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
  }
}
```

#### 响应参数说明
| 参数名 | 类型 | 描述 |
|--------|------|------|
| data.user.id | integer | 用户ID |
| data.user.email | string | 用户邮箱 |
| data.user.username | string | 用户名 |
| data.user.realName | string | 真实姓名 |
| data.user.lastLoginTime | string | 最后登录时间 |
| data.user.createdTime | string | 账户创建时间 |
| data.token | string | JWT访问令牌 |

#### 错误响应示例

**1. 参数缺失（400）**
```json
{
  "success": false,
  "message": "邮箱不能为空",
  "code": 400
}
```

**2. 验证码错误（401）**
```json
{
  "success": false,
  "message": "验证码错误或已过期",
  "code": 401
}
```

**3. 审核状态异常（403）**
```json
{
  "success": false,
  "message": "您的账户审核未通过，请联系管理员",
  "code": 403
}
```

### 使用说明
- JWT Token需要在后续请求中携带，放在Authorization头中
- Token格式：`Bearer {token}`
- 用户审核状态必须为"已通过"才能登录成功
- 登录成功后会更新用户的最后登录时间和IP地址

## 4. 获取用户信息

### 接口信息
- **接口地址：** `GET /api/v1/vue-auth/user-info`
- **接口描述：** 获取当前登录用户的详细信息
- **请求方式：** GET
- **是否需要认证：** 是

### 请求头
```
Authorization: Bearer {token}
```

### 请求参数
无

### 响应格式

#### 成功响应
```json
{
  "success": true,
  "message": "获取用户信息成功",
  "code": 200,
  "data": {
    "id": 1,
    "email": "<EMAIL>",
    "username": "user",
    "realName": "张三",
    "lastLoginTime": "2024-01-01 12:00:00",
    "createdTime": "2024-01-01 10:00:00"
  }
}
```

#### 错误响应
```json
{
  "success": false,
  "message": "Token无效或已过期",
  "code": 401
}
```

## 状态码说明

| 状态码 | 描述 | 说明 |
|--------|------|------|
| 200 | 成功 | 请求处理成功 |
| 400 | 请求参数错误 | 缺少必填参数或参数格式错误 |
| 401 | 认证失败 | 验证码错误、Token无效或已过期 |
| 403 | 权限不足 | 用户审核状态不通过 |
| 429 | 请求过于频繁 | 触发频率限制 |
| 500 | 服务器内部错误 | 系统异常 |

## 前端集成示例

### JavaScript/TypeScript 示例

```javascript
class AuthAPI {
    constructor(baseURL = '/api/v1/vue-auth') {
        this.baseURL = baseURL;
        this.captchaId = null;
    }

    // 获取图形验证码
    async getCaptcha() {
        try {
            const response = await fetch(`${this.baseURL}/captcha`);
            const result = await response.json();

            if (result.success) {
                this.captchaId = result.data.captchaId;
                return result.data;
            } else {
                throw new Error(result.message);
            }
        } catch (error) {
            console.error('获取验证码失败:', error);
            throw error;
        }
    }

    // 发送邮箱验证码
    async sendEmailCode(email, captchaCode) {
        if (!this.captchaId) {
            throw new Error('请先获取图形验证码');
        }

        try {
            const response = await fetch(`${this.baseURL}/send-code`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    email,
                    captchaId: this.captchaId,
                    captchaCode
                })
            });

            const result = await response.json();

            if (result.success) {
                // 重置验证码ID，需要重新获取
                this.captchaId = null;
                return result;
            } else {
                throw new Error(result.message);
            }
        } catch (error) {
            console.error('发送验证码失败:', error);
            throw error;
        }
    }

    // 用户登录
    async login(email, verificationCode) {
        try {
            const response = await fetch(`${this.baseURL}/login`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    email,
                    verificationCode
                })
            });

            const result = await response.json();

            if (result.success) {
                // 保存Token到localStorage
                localStorage.setItem('token', result.data.token);
                return result.data;
            } else {
                throw new Error(result.message);
            }
        } catch (error) {
            console.error('登录失败:', error);
            throw error;
        }
    }

    // 获取用户信息
    async getUserInfo() {
        const token = localStorage.getItem('token');
        if (!token) {
            throw new Error('未登录');
        }

        try {
            const response = await fetch(`${this.baseURL}/user-info`, {
                headers: {
                    'Authorization': `Bearer ${token}`
                }
            });

            const result = await response.json();

            if (result.success) {
                return result.data;
            } else {
                throw new Error(result.message);
            }
        } catch (error) {
            console.error('获取用户信息失败:', error);
            throw error;
        }
    }

    // 退出登录
    logout() {
        localStorage.removeItem('token');
        this.captchaId = null;
    }
}

// 使用示例
const authAPI = new AuthAPI();

// 完整的登录流程
async function loginFlow() {
    try {
        // 1. 获取图形验证码
        const captchaData = await authAPI.getCaptcha();
        document.getElementById('captchaImage').src = captchaData.captchaImage;

        // 2. 用户输入信息后发送邮箱验证码
        const email = document.getElementById('email').value;
        const captchaCode = document.getElementById('captchaCode').value;
        await authAPI.sendEmailCode(email, captchaCode);
        alert('验证码发送成功');

        // 3. 用户输入邮箱验证码后登录
        const verificationCode = document.getElementById('verificationCode').value;
        const loginData = await authAPI.login(email, verificationCode);
        console.log('登录成功:', loginData);

        // 4. 获取用户信息
        const userInfo = await authAPI.getUserInfo();
        console.log('用户信息:', userInfo);

    } catch (error) {
        alert('操作失败: ' + error.message);
    }
}
```

### Vue.js 组件示例

```vue
<template>
  <div class="auth-container">
    <!-- 发送验证码区域 -->
    <div class="send-code-section">
      <h3>发送邮箱验证码</h3>
      <el-form :model="sendCodeForm" :rules="sendCodeRules" ref="sendCodeFormRef">
        <el-form-item prop="email">
          <el-input v-model="sendCodeForm.email" placeholder="请输入邮箱" />
        </el-form-item>

        <el-form-item prop="captchaCode">
          <div class="captcha-container">
            <el-input
              v-model="sendCodeForm.captchaCode"
              placeholder="请输入图形验证码"
              maxlength="4"
              style="width: 200px; margin-right: 10px;"
            />
            <img
              :src="captchaImage"
              alt="图形验证码"
              @click="refreshCaptcha"
              class="captcha-image"
            />
            <el-button @click="refreshCaptcha" type="text">刷新</el-button>
          </div>
        </el-form-item>

        <el-form-item>
          <el-button
            type="primary"
            @click="sendCode"
            :loading="sendCodeLoading"
            :disabled="sendCodeCountdown > 0"
          >
            {{ sendCodeCountdown > 0 ? `${sendCodeCountdown}s后重试` : '发送验证码' }}
          </el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 登录区域 -->
    <div class="login-section">
      <h3>用户登录</h3>
      <el-form :model="loginForm" :rules="loginRules" ref="loginFormRef">
        <el-form-item prop="email">
          <el-input v-model="loginForm.email" placeholder="请输入邮箱" />
        </el-form-item>

        <el-form-item prop="verificationCode">
          <el-input
            v-model="loginForm.verificationCode"
            placeholder="请输入邮箱验证码"
            maxlength="6"
          />
        </el-form-item>

        <el-form-item>
          <el-button
            type="primary"
            @click="login"
            :loading="loginLoading"
            style="width: 100%;"
          >
            登录
          </el-button>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'

// 响应式数据
const captchaImage = ref('')
const captchaId = ref('')
const sendCodeLoading = ref(false)
const loginLoading = ref(false)
const sendCodeCountdown = ref(0)

// 表单数据
const sendCodeForm = reactive({
  email: '',
  captchaCode: ''
})

const loginForm = reactive({
  email: '',
  verificationCode: ''
})

// 表单验证规则
const sendCodeRules = {
  email: [
    { required: true, message: '请输入邮箱', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
  ],
  captchaCode: [
    { required: true, message: '请输入图形验证码', trigger: 'blur' },
    { len: 4, message: '验证码长度为4位', trigger: 'blur' }
  ]
}

const loginRules = {
  email: [
    { required: true, message: '请输入邮箱', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
  ],
  verificationCode: [
    { required: true, message: '请输入邮箱验证码', trigger: 'blur' },
    { len: 6, message: '验证码长度为6位', trigger: 'blur' }
  ]
}

// 获取图形验证码
const getCaptcha = async () => {
  try {
    const response = await fetch('/api/v1/vue-auth/captcha')
    const result = await response.json()

    if (result.success) {
      captchaImage.value = result.data.captchaImage
      captchaId.value = result.data.captchaId
    } else {
      ElMessage.error(result.message)
    }
  } catch (error) {
    ElMessage.error('获取验证码失败')
  }
}

// 刷新验证码
const refreshCaptcha = () => {
  getCaptcha()
  sendCodeForm.captchaCode = ''
}

// 发送邮箱验证码
const sendCode = async () => {
  if (!sendCodeForm.email || !sendCodeForm.captchaCode) {
    ElMessage.warning('请填写完整信息')
    return
  }

  sendCodeLoading.value = true

  try {
    const response = await fetch('/api/v1/vue-auth/send-code', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        email: sendCodeForm.email,
        captchaId: captchaId.value,
        captchaCode: sendCodeForm.captchaCode
      })
    })

    const result = await response.json()

    if (result.success) {
      ElMessage.success('验证码发送成功')
      loginForm.email = sendCodeForm.email
      startCountdown()
      refreshCaptcha()
    } else {
      ElMessage.error(result.message)
      if (result.code === 401) {
        refreshCaptcha()
      }
    }
  } catch (error) {
    ElMessage.error('发送验证码失败')
  } finally {
    sendCodeLoading.value = false
  }
}

// 倒计时
const startCountdown = () => {
  sendCodeCountdown.value = 60
  const timer = setInterval(() => {
    sendCodeCountdown.value--
    if (sendCodeCountdown.value <= 0) {
      clearInterval(timer)
    }
  }, 1000)
}

// 用户登录
const login = async () => {
  if (!loginForm.email || !loginForm.verificationCode) {
    ElMessage.warning('请填写邮箱和验证码')
    return
  }

  loginLoading.value = true

  try {
    const response = await fetch('/api/v1/vue-auth/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        email: loginForm.email,
        verificationCode: loginForm.verificationCode
      })
    })

    const result = await response.json()

    if (result.success) {
      localStorage.setItem('token', result.data.token)
      ElMessage.success('登录成功')
      // 跳转到主页或其他页面
      // router.push('/dashboard')
    } else {
      ElMessage.error(result.message)
    }
  } catch (error) {
    ElMessage.error('登录失败')
  } finally {
    loginLoading.value = false
  }
}

// 组件挂载时获取验证码
onMounted(() => {
  getCaptcha()
})
</script>

<style scoped>
.auth-container {
  max-width: 400px;
  margin: 0 auto;
  padding: 20px;
}

.send-code-section,
.login-section {
  margin-bottom: 30px;
  padding: 20px;
  border: 1px solid #eee;
  border-radius: 8px;
}

.captcha-container {
  display: flex;
  align-items: center;
}

.captcha-image {
  height: 40px;
  cursor: pointer;
  border: 1px solid #ddd;
  border-radius: 4px;
  margin-right: 10px;
}
</style>
```

## 错误处理建议

### 1. 网络错误处理
```javascript
const handleNetworkError = (error) => {
  if (error.name === 'TypeError' && error.message.includes('fetch')) {
    return '网络连接失败，请检查网络设置';
  }
  return '网络异常，请稍后重试';
};
```

### 2. Token过期处理
```javascript
const handleTokenExpired = () => {
  localStorage.removeItem('token');
  // 跳转到登录页
  window.location.href = '/login';
};
```

### 3. 统一错误处理
```javascript
const handleAPIError = (result) => {
  switch (result.code) {
    case 401:
      if (result.message.includes('Token')) {
        handleTokenExpired();
      }
      break;
    case 403:
      // 审核状态异常，显示具体信息
      break;
    case 429:
      // 频率限制，建议用户稍后重试
      break;
    default:
      // 其他错误
      break;
  }
};
```

## 安全建议

1. **HTTPS传输**：生产环境必须使用HTTPS协议
2. **Token存储**：建议使用httpOnly Cookie存储Token
3. **输入验证**：前端需要对用户输入进行基础验证
4. **错误信息**：避免在错误信息中泄露敏感信息
5. **频率限制**：遵守API的频率限制规则
6. **验证码刷新**：验证失败后及时刷新图形验证码

## 更新日志

### v1.0.0 (2024-01-01)
- 新增图形验证码生成接口
- 升级发送验证码接口，增加图形验证码校验
- 新增用户审核状态检查
- 完善错误处理和状态码规范

---

**技术支持：** SinoairAgent Team
**文档版本：** v1.0.0
**最后更新：** 2024-01-01
