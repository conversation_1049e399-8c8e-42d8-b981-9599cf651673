# 环境配置说明

## 概述

本项目支持多环境配置，可以根据不同的环境参数启动对应的配置。

## 环境类型

### 1. 开发环境 (development)
- **配置文件**: `.env.development`
- **API地址**: `http://localhost:9091`
- **端口**: `3000`
- **代理**: 启用
- **调试**: 启用

### 2. 测试环境 (test)
- **配置文件**: `.env.test`
- **API地址**: `http://test-api.sinoair.com:9091`
- **端口**: `3001`
- **代理**: 禁用
- **调试**: 启用

### 3. 预生产环境 (staging)
- **配置文件**: `.env.staging`
- **API地址**: `https://staging-api.sinoair.com`
- **端口**: `3002`
- **代理**: 禁用
- **调试**: 禁用

### 4. 生产环境 (production)
- **配置文件**: `.env.production`
- **API地址**: `https://api.sinoair.com`
- **端口**: `3000`
- **代理**: 禁用
- **调试**: 禁用

## 启动方式

### 使用npm脚本

```bash
# 开发环境
npm run dev

# 测试环境
npm run dev:test

# 预生产环境
npm run dev:staging

# 生产环境
npm run dev:prod
```

### 使用启动脚本

**Windows:**
```cmd
# 开发环境
start.bat

# 测试环境
start.bat test

# 预生产环境
start.bat staging

# 生产环境
start.bat prod
```

**Linux/Mac:**
```bash
# 开发环境
./start.sh

# 测试环境
./start.sh test

# 预生产环境
./start.sh staging

# 生产环境
./start.sh production
```

## 构建命令

```bash
# 开发环境构建
npm run build:dev

# 测试环境构建
npm run build:test

# 预生产环境构建
npm run build:staging

# 生产环境构建
npm run build:prod
```

## 环境变量说明

| 变量名 | 说明 | 示例 |
|--------|------|------|
| `VITE_APP_TITLE` | 应用标题 | `SINOAIR-AGENT 智能体门户` |
| `VITE_APP_DESCRIPTION` | 应用描述 | `基于AI的智能文档处理平台` |
| `VITE_APP_ENV` | 环境标识 | `development` |
| `VITE_API_BASE_URL` | API基础地址 | `http://localhost:9091` |
| `VITE_API_TIMEOUT` | API超时时间 | `30000` |
| `VITE_DEV_PORT` | 开发服务器端口 | `3000` |
| `VITE_DEV_HOST` | 开发服务器主机 | `localhost` |
| `VITE_DEBUG` | 调试模式 | `true` |
| `VITE_CONSOLE_LOG` | 控制台日志 | `true` |
| `VITE_PROXY_ENABLED` | 是否启用代理 | `true` |
| `VITE_PROXY_TARGET` | 代理目标地址 | `http://localhost:9091` |

## 自定义环境配置

1. 复制现有的环境配置文件
2. 修改相应的环境变量
3. 在 `package.json` 中添加对应的脚本
4. 更新启动脚本以支持新环境

## 注意事项

1. 环境变量必须以 `VITE_` 开头才能在前端代码中访问
2. 开发环境建议启用代理以避免跨域问题
3. 生产环境应禁用调试模式以提高性能
4. 修改环境配置后需要重启开发服务器
