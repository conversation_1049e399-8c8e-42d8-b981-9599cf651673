@echo off
echo ========================================
echo SINOAIR-AGENT Vue.js 前端启动脚本
echo ========================================
echo.

:: 解析命令行参数
set ENV_MODE=development
if "%1"=="test" set ENV_MODE=test
if "%1"=="staging" set ENV_MODE=staging
if "%1"=="prod" set ENV_MODE=production
if "%1"=="production" set ENV_MODE=production

echo [信息] 启动环境: %ENV_MODE%
echo.

:: 检查 Node.js 是否安装
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [错误] 未检测到 Node.js，请先安装 Node.js
    echo 下载地址: https://nodejs.org/
    pause
    exit /b 1
)

echo [信息] Node.js 版本:
node --version
echo.

:: 检查是否存在 node_modules
if not exist "node_modules" (
    echo [信息] 首次运行，正在安装依赖...
    echo.
    npm install
    if %errorlevel% neq 0 (
        echo [错误] 依赖安装失败
        pause
        exit /b 1
    )
    echo.
    echo [成功] 依赖安装完成
    echo.
)

:: 检查环境配置文件
if not exist ".env.local" (
    if exist ".env.example" (
        echo [信息] 创建环境配置文件...
        copy ".env.example" ".env.local"
        echo [成功] 已创建 .env.local 文件
        echo.
    )
)

:: 启动开发服务器
echo [信息] 启动开发服务器...
echo [信息] 环境模式: %ENV_MODE%
echo [信息] 按 Ctrl+C 停止服务器
echo.
echo ========================================
echo.

if "%ENV_MODE%"=="development" npm run dev
if "%ENV_MODE%"=="test" npm run dev:test
if "%ENV_MODE%"=="staging" npm run dev:staging
if "%ENV_MODE%"=="production" npm run dev:prod

pause
